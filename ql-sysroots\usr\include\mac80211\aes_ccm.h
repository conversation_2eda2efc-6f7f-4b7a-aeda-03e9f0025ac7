/* SPDX-License-Identifier: GPL-2.0-only */
/*
 * Copyright 2003-2004, Instant802 Networks, Inc.
 * Copyright 2006, Devicescape Software, Inc.
 */

#ifndef AES_CCM_H
#define AES_CCM_H

#include <linux/crypto.h>

struct crypto_cipher *ieee80211_aes_key_setup_encrypt(const u8 key[],
						      size_t key_len,
						      size_t mic_len);
void ieee80211_aes_ccm_encrypt(struct crypto_cipher *tfm, u8 *b_0, u8 *aad,
			       u8 *data, size_t data_len, u8 *mic,
			       size_t mic_len);
int ieee80211_aes_ccm_decrypt(struct crypto_cipher *tfm, u8 *b_0, u8 *aad,
			      u8 *data, size_t data_len, u8 *mic,
			      size_t mic_len);
void ieee80211_aes_key_free(struct crypto_cipher *tfm);

#endif /* AES_CCM_H */
