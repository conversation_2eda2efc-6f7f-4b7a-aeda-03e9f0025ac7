/* SPDX-License-Identifier: GPL-2.0-or-later */
/* ASN.1 Object identifier (OID) registry
 *
 * Copyright (C) 2012 Red Hat, Inc. All Rights Reserved.
 * Written by <PERSON> (<EMAIL>)
 */

#ifndef _LINUX_OID_REGISTRY_H
#define _LINUX_OID_REGISTRY_H

#include <linux/types.h>

/*
 * OIDs are turned into these values if possible, or OID__NR if not held here.
 *
 * NOTE!  Do not mess with the format of each line as this is read by
 *	  build_OID_registry.pl to generate the data for look_up_OID().
 */
enum OID {
	OID_id_dsa_with_sha1,		/* 1.2.840.10030.4.3 */
	OID_id_dsa,			/* 1.2.840.10040.4.1 */
	OID_id_ecdsa_with_sha1,		/* 1.2.840.10045.4.1 */
	OID_id_ecPublicKey,		/* 1.2.840.10045.2.1 */

	/* PKCS#1 {iso(1) member-body(2) us(840) rsadsi(113549) pkcs(1) pkcs-1(1)} */
	OID_rsaEncryption,		/* 1.2.840.113549.1.1.1 */
	OID_md2WithRSAEncryption,	/* 1.2.840.113549.1.1.2 */
	OID_md3WithRSAEncryption,	/* 1.2.840.113549.1.1.3 */
	OID_md4WithRSAEncryption,	/* 1.2.840.113549.1.1.4 */
	OID_sha1WithRSAEncryption,	/* 1.2.840.113549.1.1.5 */
	OID_sha256WithRSAEncryption,	/* 1.2.840.113549.1.1.11 */
	OID_sha384WithRSAEncryption,	/* 1.2.840.113549.1.1.12 */
	OID_sha512WithRSAEncryption,	/* 1.2.840.113549.1.1.13 */
	OID_sha224WithRSAEncryption,	/* 1.2.840.113549.1.1.14 */
	/* PKCS#7 {iso(1) member-body(2) us(840) rsadsi(113549) pkcs(1) pkcs-7(7)} */
	OID_data,			/* 1.2.840.113549.1.7.1 */
	OID_signed_data,		/* 1.2.840.113549.1.7.2 */
	/* PKCS#9 {iso(1) member-body(2) us(840) rsadsi(113549) pkcs(1) pkcs-9(9)} */
	OID_email_address,		/* 1.2.840.113549.1.9.1 */
	OID_contentType,		/* 1.2.840.113549.1.9.3 */
	OID_messageDigest,		/* 1.2.840.113549.1.9.4 */
	OID_signingTime,		/* 1.2.840.113549.1.9.5 */
	OID_smimeCapabilites,		/* 1.2.840.113549.1.9.15 */
	OID_smimeAuthenticatedAttrs,	/* 1.2.840.113549.********.11 */

	/* {iso(1) member-body(2) us(840) rsadsi(113549) digestAlgorithm(2)} */
	OID_md2,			/* 1.2.840.113549.2.2 */
	OID_md4,			/* 1.2.840.113549.2.4 */
	OID_md5,			/* 1.2.840.113549.2.5 */

	/* Microsoft Authenticode & Software Publishing */
	OID_msIndirectData,		/* *******.4.1.311.2.1.4 */
	OID_msStatementType,		/* *******.4.1.311.2.1.11 */
	OID_msSpOpusInfo,		/* *******.4.1.311.2.1.12 */
	OID_msPeImageDataObjId,		/* *******.4.1.311.2.1.15 */
	OID_msIndividualSPKeyPurpose,	/* *******.4.1.311.2.1.21 */
	OID_msOutlookExpress,		/* *******.4.1.311.16.4 */

	OID_certAuthInfoAccess,		/* *******.*******.1 */
	OID_sha1,			/* ********.2.26 */
	OID_sha256,			/* 2.16.840.*********.2.1 */
	OID_sha384,			/* 2.16.840.*********.2.2 */
	OID_sha512,			/* 2.16.840.*********.2.3 */
	OID_sha224,			/* 2.16.840.*********.2.4 */

	/* Distinguished Name attribute IDs [RFC 2256] */
	OID_commonName,			/* ******* */
	OID_surname,			/* ******* */
	OID_countryName,		/* ******* */
	OID_locality,			/* ******* */
	OID_stateOrProvinceName,	/* ******* */
	OID_organizationName,		/* ******** */
	OID_organizationUnitName,	/* ******** */
	OID_title,			/* ******** */
	OID_description,		/* ******** */
	OID_name,			/* ******** */
	OID_givenName,			/* *******2 */
	OID_initials,			/* *******3 */
	OID_generationalQualifier,	/* *******4 */

	/* Certificate extension IDs */
	OID_subjectKeyIdentifier,	/* ********* */
	OID_keyUsage,			/* ********* */
	OID_subjectAltName,		/* ********* */
	OID_issuerAltName,		/* ********* */
	OID_basicConstraints,		/* ********* */
	OID_crlDistributionPoints,	/* ********* */
	OID_certPolicies,		/* ********* */
	OID_authorityKeyIdentifier,	/* ********* */
	OID_extKeyUsage,		/* ********* */

	/* EC-RDSA */
	OID_gostCPSignA,		/* 1.2.643.******** */
	OID_gostCPSignB,		/* 1.2.643.******** */
	OID_gostCPSignC,		/* 1.2.643.******** */
	OID_gost2012PKey256,		/* 1.2.643.*******.1 */
	OID_gost2012PKey512,		/* 1.2.643.*******.2 */
	OID_gost2012Digest256,		/* 1.2.643.*******.2 */
	OID_gost2012Digest512,		/* 1.2.643.*******.3 */
	OID_gost2012Signature256,	/* 1.2.643.*******.2 */
	OID_gost2012Signature512,	/* 1.2.643.*******.3 */
	OID_gostTC26Sign256A,		/* 1.2.643.7.1.2.1.1.1 */
	OID_gostTC26Sign256B,		/* 1.2.643.7.1.2.1.1.2 */
	OID_gostTC26Sign256C,		/* 1.2.643.7.1.2.1.1.3 */
	OID_gostTC26Sign256D,		/* 1.2.643.7.1.2.1.1.4 */
	OID_gostTC26Sign512A,		/* 1.2.643.7.1.2.1.2.1 */
	OID_gostTC26Sign512B,		/* 1.2.643.7.1.2.1.2.2 */
	OID_gostTC26Sign512C,		/* 1.2.643.7.1.2.1.2.3 */

	/* OSCCA */
	OID_sm2,			/* 1.2.156.10197.1.301 */
	OID_sm3,			/* 1.2.156.10197.1.401 */
	OID_SM2_with_SM3,		/* 1.2.156.10197.1.501 */
	OID_sm3WithRSAEncryption,	/* 1.2.156.10197.1.504 */

	OID__NR
};

extern enum OID look_up_OID(const void *data, size_t datasize);
extern int sprint_oid(const void *, size_t, char *, size_t);
extern int sprint_OID(enum OID, char *, size_t);

#endif /* _LINUX_OID_REGISTRY_H */
