/* Version information about elfutils development libraries.
   Copyright (C) 2008 Red Hat, Inc.

   This file is part of elfutils.

   This file is free software; you can redistribute it and/or modify
   it under the terms of either

     * the GNU Lesser General Public License as published by the Free
       Software Foundation; either version 3 of the License, or (at
       your option) any later version

   or

     * the GNU General Public License as published by the Free
       Software Foundation; either version 2 of the License, or (at
       your option) any later version

   or both in parallel, as here.

   elfutils is distributed in the hope that it will be useful, but
   WITHOUT ANY WARRANTY; without even the implied warranty of
   MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
   General Public License for more details.

   You should have received copies of the GNU General Public License and
   the GNU Lesser General Public License along with this program.  If
   not, see <http://www.gnu.org/licenses/>.  */

#ifndef _ELFUTILS_VERSION_H
#define _ELFUTILS_VERSION_H	1

#define _ELFUTILS_VERSION	180

#define _ELFUTILS_PREREQ(major, minor) \
  (_ELFUTILS_VERSION >= ((major) * 1000 + (minor)))

#endif	/* elfutils/version.h */
