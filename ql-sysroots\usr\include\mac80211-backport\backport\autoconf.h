#ifndef COMPAT_AUTOCONF_INCLUDED
#define COMPAT_AUTOCONF_INCLUDED
/*
 * Automatically generated file, don't edit!
 * Changes will be overwritten
 */

#define CPTCFG_WIRELESS 1
#define CPTCFG_NET_CORE 1
#define CPTCFG_EXPERT 1
#define CPTCFG_BP_MODULES 1
#define CPTCFG_BPAUTO_LEDS_TRIGGERS 1
#define CPTCFG_BPAUTO_CRYPTO_LIB_ARC4 1
#define CPTCFG_CFG80211_MODULE 1
#define CPTCFG_CFG80211_CERTIFICATION_ONUS 1
#define CPTCFG_CFG80211_WEXT 1
#define CPTCFG_MAC80211_MODULE 1
#define CPTCFG_MAC80211_HAS_RC 1
#define CPTCFG_MAC80211_RC_MINSTREL 1
#define CPTCFG_MAC80211_RC_DEFAULT_MINSTREL 1
#define CPTCFG_MAC80211_RC_DEFAULT "minstrel_ht"
#define CPTCFG_MAC80211_LEDS 1
#define CPTCFG_MAC80211_STA_HASH_MAX_SIZE 0
#define CPTCFG_WLAN 1
#define CPTCFG_WLAN_VENDOR_ADMTEK 1
#define CPTCFG_WLAN_VENDOR_ATH 1
#define CPTCFG_ATH5K_PCI 1
#define CPTCFG_WLAN_VENDOR_ATMEL 1
#define CPTCFG_WLAN_VENDOR_BROADCOM 1
#define CPTCFG_WLAN_VENDOR_CISCO 1
#define CPTCFG_WLAN_VENDOR_INTEL 1
#define CPTCFG_WLAN_VENDOR_INTERSIL 1
#define CPTCFG_WLAN_VENDOR_MARVELL 1
#define CPTCFG_WLAN_VENDOR_MEDIATEK 1
#define CPTCFG_WLAN_VENDOR_RALINK 1
#define CPTCFG_WLAN_VENDOR_REALTEK 1
#define CPTCFG_WLAN_VENDOR_RSI 1
#define CPTCFG_WLAN_VENDOR_ST 1
#define CPTCFG_WLAN_VENDOR_TI 1
#define CPTCFG_WLAN_VENDOR_ZYDAS 1
#define CPTCFG_SSB_POSSIBLE 1
#define CPTCFG_BCMA_POSSIBLE 1
#define CPTCFG_STAGING 1
#define CPTCFG_BACKPORTED_WIRELESS 1
#define CPTCFG_BACKPORTED_NET_CORE 1
#define CPTCFG_BACKPORTED_EXPERT 1
#define CPTCFG_BACKPORTED_BP_MODULES 1
#define CPTCFG_BACKPORTED_BPAUTO_LEDS_TRIGGERS 1
#define CPTCFG_BACKPORTED_BPAUTO_CRYPTO_LIB_ARC4 1
#define CPTCFG_BACKPORTED_CFG80211_MODULE 1
#define CPTCFG_BACKPORTED_CFG80211_CERTIFICATION_ONUS 1
#define CPTCFG_BACKPORTED_CFG80211_WEXT 1
#define CPTCFG_BACKPORTED_MAC80211_MODULE 1
#define CPTCFG_BACKPORTED_MAC80211_HAS_RC 1
#define CPTCFG_BACKPORTED_MAC80211_RC_MINSTREL 1
#define CPTCFG_BACKPORTED_MAC80211_RC_DEFAULT_MINSTREL 1
#define CPTCFG_BACKPORTED_MAC80211_LEDS 1
#define CPTCFG_BACKPORTED_WLAN 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_ADMTEK 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_ATH 1
#define CPTCFG_BACKPORTED_ATH5K_PCI 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_ATMEL 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_BROADCOM 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_CISCO 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_INTEL 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_INTERSIL 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_MARVELL 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_MEDIATEK 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_RALINK 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_REALTEK 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_RSI 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_ST 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_TI 1
#define CPTCFG_BACKPORTED_WLAN_VENDOR_ZYDAS 1
#define CPTCFG_BACKPORTED_SSB_POSSIBLE 1
#define CPTCFG_BACKPORTED_BCMA_POSSIBLE 1
#define CPTCFG_BACKPORTED_STAGING 1
#endif /* COMPAT_AUTOCONF_INCLUDED */
