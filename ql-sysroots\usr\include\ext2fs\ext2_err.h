/*
 * ext2_err.h:
 * This file is automatically generated; please do not edit it.
 */

#include <et/com_err.h>

#define EXT2_ET_BASE                             (2133571328L)
#define EXT2_ET_MAGIC_EXT2FS_FILSYS              (2133571329L)
#define EXT2_ET_MAGIC_BADBLOCKS_LIST             (2133571330L)
#define EXT2_ET_MAGIC_BADBLOCKS_ITERATE          (2133571331L)
#define EXT2_ET_MAGIC_INODE_SCAN                 (2133571332L)
#define EXT2_ET_MAGIC_IO_CHANNEL                 (2133571333L)
#define EXT2_ET_MAGIC_UNIX_IO_CHANNEL            (2133571334L)
#define EXT2_ET_MAGIC_IO_MANAGER                 (2133571335L)
#define EXT2_ET_MAGIC_BLOCK_BITMAP               (2133571336L)
#define EXT2_ET_MAGIC_INODE_BITMAP               (2133571337L)
#define EXT2_ET_MAGIC_GENERIC_BITMAP             (2133571338L)
#define EXT2_ET_MAGIC_TEST_IO_CHANNEL            (2133571339L)
#define EXT2_ET_MAGIC_DBLIST                     (2133571340L)
#define EXT2_ET_MAGIC_ICOUNT                     (2133571341L)
#define EXT2_ET_MAGIC_PQ_IO_CHANNEL              (2133571342L)
#define EXT2_ET_MAGIC_EXT2_FILE                  (2133571343L)
#define EXT2_ET_MAGIC_E2IMAGE                    (2133571344L)
#define EXT2_ET_MAGIC_INODE_IO_CHANNEL           (2133571345L)
#define EXT2_ET_MAGIC_EXTENT_HANDLE              (2133571346L)
#define EXT2_ET_BAD_MAGIC                        (2133571347L)
#define EXT2_ET_REV_TOO_HIGH                     (2133571348L)
#define EXT2_ET_RO_FILSYS                        (2133571349L)
#define EXT2_ET_GDESC_READ                       (2133571350L)
#define EXT2_ET_GDESC_WRITE                      (2133571351L)
#define EXT2_ET_GDESC_BAD_BLOCK_MAP              (2133571352L)
#define EXT2_ET_GDESC_BAD_INODE_MAP              (2133571353L)
#define EXT2_ET_GDESC_BAD_INODE_TABLE            (2133571354L)
#define EXT2_ET_INODE_BITMAP_WRITE               (2133571355L)
#define EXT2_ET_INODE_BITMAP_READ                (2133571356L)
#define EXT2_ET_BLOCK_BITMAP_WRITE               (2133571357L)
#define EXT2_ET_BLOCK_BITMAP_READ                (2133571358L)
#define EXT2_ET_INODE_TABLE_WRITE                (2133571359L)
#define EXT2_ET_INODE_TABLE_READ                 (2133571360L)
#define EXT2_ET_NEXT_INODE_READ                  (2133571361L)
#define EXT2_ET_UNEXPECTED_BLOCK_SIZE            (2133571362L)
#define EXT2_ET_DIR_CORRUPTED                    (2133571363L)
#define EXT2_ET_SHORT_READ                       (2133571364L)
#define EXT2_ET_SHORT_WRITE                      (2133571365L)
#define EXT2_ET_DIR_NO_SPACE                     (2133571366L)
#define EXT2_ET_NO_INODE_BITMAP                  (2133571367L)
#define EXT2_ET_NO_BLOCK_BITMAP                  (2133571368L)
#define EXT2_ET_BAD_INODE_NUM                    (2133571369L)
#define EXT2_ET_BAD_BLOCK_NUM                    (2133571370L)
#define EXT2_ET_EXPAND_DIR_ERR                   (2133571371L)
#define EXT2_ET_TOOSMALL                         (2133571372L)
#define EXT2_ET_BAD_BLOCK_MARK                   (2133571373L)
#define EXT2_ET_BAD_BLOCK_UNMARK                 (2133571374L)
#define EXT2_ET_BAD_BLOCK_TEST                   (2133571375L)
#define EXT2_ET_BAD_INODE_MARK                   (2133571376L)
#define EXT2_ET_BAD_INODE_UNMARK                 (2133571377L)
#define EXT2_ET_BAD_INODE_TEST                   (2133571378L)
#define EXT2_ET_FUDGE_BLOCK_BITMAP_END           (2133571379L)
#define EXT2_ET_FUDGE_INODE_BITMAP_END           (2133571380L)
#define EXT2_ET_BAD_IND_BLOCK                    (2133571381L)
#define EXT2_ET_BAD_DIND_BLOCK                   (2133571382L)
#define EXT2_ET_BAD_TIND_BLOCK                   (2133571383L)
#define EXT2_ET_NEQ_BLOCK_BITMAP                 (2133571384L)
#define EXT2_ET_NEQ_INODE_BITMAP                 (2133571385L)
#define EXT2_ET_BAD_DEVICE_NAME                  (2133571386L)
#define EXT2_ET_MISSING_INODE_TABLE              (2133571387L)
#define EXT2_ET_CORRUPT_SUPERBLOCK               (2133571388L)
#define EXT2_ET_BAD_GENERIC_MARK                 (2133571389L)
#define EXT2_ET_BAD_GENERIC_UNMARK               (2133571390L)
#define EXT2_ET_BAD_GENERIC_TEST                 (2133571391L)
#define EXT2_ET_SYMLINK_LOOP                     (2133571392L)
#define EXT2_ET_CALLBACK_NOTHANDLED              (2133571393L)
#define EXT2_ET_BAD_BLOCK_IN_INODE_TABLE         (2133571394L)
#define EXT2_ET_UNSUPP_FEATURE                   (2133571395L)
#define EXT2_ET_RO_UNSUPP_FEATURE                (2133571396L)
#define EXT2_ET_LLSEEK_FAILED                    (2133571397L)
#define EXT2_ET_NO_MEMORY                        (2133571398L)
#define EXT2_ET_INVALID_ARGUMENT                 (2133571399L)
#define EXT2_ET_BLOCK_ALLOC_FAIL                 (2133571400L)
#define EXT2_ET_INODE_ALLOC_FAIL                 (2133571401L)
#define EXT2_ET_NO_DIRECTORY                     (2133571402L)
#define EXT2_ET_TOO_MANY_REFS                    (2133571403L)
#define EXT2_ET_FILE_NOT_FOUND                   (2133571404L)
#define EXT2_ET_FILE_RO                          (2133571405L)
#define EXT2_ET_DB_NOT_FOUND                     (2133571406L)
#define EXT2_ET_DIR_EXISTS                       (2133571407L)
#define EXT2_ET_UNIMPLEMENTED                    (2133571408L)
#define EXT2_ET_CANCEL_REQUESTED                 (2133571409L)
#define EXT2_ET_FILE_TOO_BIG                     (2133571410L)
#define EXT2_ET_JOURNAL_NOT_BLOCK                (2133571411L)
#define EXT2_ET_NO_JOURNAL_SB                    (2133571412L)
#define EXT2_ET_JOURNAL_TOO_SMALL                (2133571413L)
#define EXT2_ET_JOURNAL_UNSUPP_VERSION           (2133571414L)
#define EXT2_ET_LOAD_EXT_JOURNAL                 (2133571415L)
#define EXT2_ET_NO_JOURNAL                       (2133571416L)
#define EXT2_ET_DIRHASH_UNSUPP                   (2133571417L)
#define EXT2_ET_BAD_EA_BLOCK_NUM                 (2133571418L)
#define EXT2_ET_TOO_MANY_INODES                  (2133571419L)
#define EXT2_ET_NOT_IMAGE_FILE                   (2133571420L)
#define EXT2_ET_RES_GDT_BLOCKS                   (2133571421L)
#define EXT2_ET_RESIZE_INODE_CORRUPT             (2133571422L)
#define EXT2_ET_SET_BMAP_NO_IND                  (2133571423L)
#define EXT2_ET_TDB_SUCCESS                      (2133571424L)
#define EXT2_ET_TDB_ERR_CORRUPT                  (2133571425L)
#define EXT2_ET_TDB_ERR_IO                       (2133571426L)
#define EXT2_ET_TDB_ERR_LOCK                     (2133571427L)
#define EXT2_ET_TDB_ERR_OOM                      (2133571428L)
#define EXT2_ET_TDB_ERR_EXISTS                   (2133571429L)
#define EXT2_ET_TDB_ERR_NOLOCK                   (2133571430L)
#define EXT2_ET_TDB_ERR_EINVAL                   (2133571431L)
#define EXT2_ET_TDB_ERR_NOEXIST                  (2133571432L)
#define EXT2_ET_TDB_ERR_RDONLY                   (2133571433L)
#define EXT2_ET_DBLIST_EMPTY                     (2133571434L)
#define EXT2_ET_RO_BLOCK_ITERATE                 (2133571435L)
#define EXT2_ET_MAGIC_EXTENT_PATH                (2133571436L)
#define EXT2_ET_MAGIC_GENERIC_BITMAP64           (2133571437L)
#define EXT2_ET_MAGIC_BLOCK_BITMAP64             (2133571438L)
#define EXT2_ET_MAGIC_INODE_BITMAP64             (2133571439L)
#define EXT2_ET_MAGIC_RESERVED_13                (2133571440L)
#define EXT2_ET_MAGIC_RESERVED_14                (2133571441L)
#define EXT2_ET_MAGIC_RESERVED_15                (2133571442L)
#define EXT2_ET_MAGIC_RESERVED_16                (2133571443L)
#define EXT2_ET_MAGIC_RESERVED_17                (2133571444L)
#define EXT2_ET_MAGIC_RESERVED_18                (2133571445L)
#define EXT2_ET_MAGIC_RESERVED_19                (2133571446L)
#define EXT2_ET_EXTENT_HEADER_BAD                (2133571447L)
#define EXT2_ET_EXTENT_INDEX_BAD                 (2133571448L)
#define EXT2_ET_EXTENT_LEAF_BAD                  (2133571449L)
#define EXT2_ET_EXTENT_NO_SPACE                  (2133571450L)
#define EXT2_ET_INODE_NOT_EXTENT                 (2133571451L)
#define EXT2_ET_EXTENT_NO_NEXT                   (2133571452L)
#define EXT2_ET_EXTENT_NO_PREV                   (2133571453L)
#define EXT2_ET_EXTENT_NO_UP                     (2133571454L)
#define EXT2_ET_EXTENT_NO_DOWN                   (2133571455L)
#define EXT2_ET_NO_CURRENT_NODE                  (2133571456L)
#define EXT2_ET_OP_NOT_SUPPORTED                 (2133571457L)
#define EXT2_ET_CANT_INSERT_EXTENT               (2133571458L)
#define EXT2_ET_CANT_SPLIT_EXTENT                (2133571459L)
#define EXT2_ET_EXTENT_NOT_FOUND                 (2133571460L)
#define EXT2_ET_EXTENT_NOT_SUPPORTED             (2133571461L)
#define EXT2_ET_EXTENT_INVALID_LENGTH            (2133571462L)
#define EXT2_ET_IO_CHANNEL_NO_SUPPORT_64         (2133571463L)
#define EXT2_ET_NO_MTAB_FILE                     (2133571464L)
#define EXT2_ET_CANT_USE_LEGACY_BITMAPS          (2133571465L)
#define EXT2_ET_MMP_MAGIC_INVALID                (2133571466L)
#define EXT2_ET_MMP_FAILED                       (2133571467L)
#define EXT2_ET_MMP_FSCK_ON                      (2133571468L)
#define EXT2_ET_MMP_BAD_BLOCK                    (2133571469L)
#define EXT2_ET_MMP_UNKNOWN_SEQ                  (2133571470L)
#define EXT2_ET_MMP_CHANGE_ABORT                 (2133571471L)
#define EXT2_ET_MMP_OPEN_DIRECT                  (2133571472L)
#define EXT2_ET_BAD_DESC_SIZE                    (2133571473L)
#define EXT2_ET_INODE_CSUM_INVALID               (2133571474L)
#define EXT2_ET_INODE_BITMAP_CSUM_INVALID        (2133571475L)
#define EXT2_ET_EXTENT_CSUM_INVALID              (2133571476L)
#define EXT2_ET_DIR_NO_SPACE_FOR_CSUM            (2133571477L)
#define EXT2_ET_DIR_CSUM_INVALID                 (2133571478L)
#define EXT2_ET_EXT_ATTR_CSUM_INVALID            (2133571479L)
#define EXT2_ET_SB_CSUM_INVALID                  (2133571480L)
#define EXT2_ET_UNKNOWN_CSUM                     (2133571481L)
#define EXT2_ET_MMP_CSUM_INVALID                 (2133571482L)
#define EXT2_ET_FILE_EXISTS                      (2133571483L)
#define EXT2_ET_BLOCK_BITMAP_CSUM_INVALID        (2133571484L)
#define EXT2_ET_INLINE_DATA_CANT_ITERATE         (2133571485L)
#define EXT2_ET_EA_BAD_NAME_LEN                  (2133571486L)
#define EXT2_ET_EA_BAD_VALUE_SIZE                (2133571487L)
#define EXT2_ET_BAD_EA_HASH                      (2133571488L)
#define EXT2_ET_BAD_EA_HEADER                    (2133571489L)
#define EXT2_ET_EA_KEY_NOT_FOUND                 (2133571490L)
#define EXT2_ET_EA_NO_SPACE                      (2133571491L)
#define EXT2_ET_MISSING_EA_FEATURE               (2133571492L)
#define EXT2_ET_NO_INLINE_DATA                   (2133571493L)
#define EXT2_ET_INLINE_DATA_NO_BLOCK             (2133571494L)
#define EXT2_ET_INLINE_DATA_NO_SPACE             (2133571495L)
#define EXT2_ET_MAGIC_EA_HANDLE                  (2133571496L)
#define EXT2_ET_INODE_IS_GARBAGE                 (2133571497L)
#define EXT2_ET_EA_BAD_VALUE_OFFSET              (2133571498L)
#define EXT2_ET_JOURNAL_FLAGS_WRONG              (2133571499L)
#define EXT2_ET_UNDO_FILE_CORRUPT                (2133571500L)
#define EXT2_ET_UNDO_FILE_WRONG                  (2133571501L)
#define EXT2_ET_FILESYSTEM_CORRUPTED             (2133571502L)
#define EXT2_ET_BAD_CRC                          (2133571503L)
#define EXT2_ET_CORRUPT_JOURNAL_SB               (2133571504L)
#define EXT2_ET_INODE_CORRUPTED                  (2133571505L)
#define EXT2_ET_EA_INODE_CORRUPTED               (2133571506L)
#define EXT2_ET_NO_GDESC                         (2133571507L)
extern const struct error_table et_ext2_error_table;
extern void initialize_ext2_error_table(void);

/* For compatibility with Heimdal */
extern void initialize_ext2_error_table_r(struct et_list **list);

#define ERROR_TABLE_BASE_ext2 (2133571328L)

/* for compatibility with older versions... */
#define init_ext2_err_tbl initialize_ext2_error_table
#define ext2_err_base ERROR_TABLE_BASE_ext2
