/* Generated by config/known-dwarf.awk from libdw/dwarf.h contents.  */

#define DWARF_ALL_KNOWN_DW_ACCESS \
  DWARF_ONE_KNOWN_DW_ACCESS (private, DW_ACCESS_private) \
  DWARF_ONE_KNOWN_DW_ACCESS (protected, DW_ACCESS_protected) \
  DWARF_ONE_KNOWN_DW_ACCESS (public, DW_ACCESS_public) \
  /* End of DW_ACCESS_*.  */

#define DWARF_ALL_KNOWN_DW_AT \
  DWARF_ONE_KNOWN_DW_AT (GNU_addr_base, DW_AT_GNU_addr_base) \
  DWARF_ONE_KNOWN_DW_AT (GNU_all_call_sites, DW_AT_GNU_all_call_sites) \
  DWARF_ONE_KNOWN_DW_AT (GNU_all_source_call_sites, DW_AT_GNU_all_source_call_sites) \
  DWARF_ONE_KNOWN_DW_AT (GNU_all_tail_call_sites, DW_AT_GNU_all_tail_call_sites) \
  DWARF_ONE_KNOWN_DW_AT (GNU_bias, DW_AT_GNU_bias) \
  DWARF_ONE_KNOWN_DW_AT (GNU_call_site_data_value, DW_AT_GNU_call_site_data_value) \
  DWARF_ONE_KNOWN_DW_AT (GNU_call_site_target, DW_AT_GNU_call_site_target) \
  DWARF_ONE_KNOWN_DW_AT (GNU_call_site_target_clobbered, DW_AT_GNU_call_site_target_clobbered) \
  DWARF_ONE_KNOWN_DW_AT (GNU_call_site_value, DW_AT_GNU_call_site_value) \
  DWARF_ONE_KNOWN_DW_AT (GNU_deleted, DW_AT_GNU_deleted) \
  DWARF_ONE_KNOWN_DW_AT (GNU_denominator, DW_AT_GNU_denominator) \
  DWARF_ONE_KNOWN_DW_AT (GNU_dwo_id, DW_AT_GNU_dwo_id) \
  DWARF_ONE_KNOWN_DW_AT (GNU_dwo_name, DW_AT_GNU_dwo_name) \
  DWARF_ONE_KNOWN_DW_AT (GNU_entry_view, DW_AT_GNU_entry_view) \
  DWARF_ONE_KNOWN_DW_AT (GNU_exclusive_locks_required, DW_AT_GNU_exclusive_locks_required) \
  DWARF_ONE_KNOWN_DW_AT (GNU_guarded, DW_AT_GNU_guarded) \
  DWARF_ONE_KNOWN_DW_AT (GNU_guarded_by, DW_AT_GNU_guarded_by) \
  DWARF_ONE_KNOWN_DW_AT (GNU_locks_excluded, DW_AT_GNU_locks_excluded) \
  DWARF_ONE_KNOWN_DW_AT (GNU_locviews, DW_AT_GNU_locviews) \
  DWARF_ONE_KNOWN_DW_AT (GNU_macros, DW_AT_GNU_macros) \
  DWARF_ONE_KNOWN_DW_AT (GNU_numerator, DW_AT_GNU_numerator) \
  DWARF_ONE_KNOWN_DW_AT (GNU_odr_signature, DW_AT_GNU_odr_signature) \
  DWARF_ONE_KNOWN_DW_AT (GNU_pt_guarded, DW_AT_GNU_pt_guarded) \
  DWARF_ONE_KNOWN_DW_AT (GNU_pt_guarded_by, DW_AT_GNU_pt_guarded_by) \
  DWARF_ONE_KNOWN_DW_AT (GNU_pubnames, DW_AT_GNU_pubnames) \
  DWARF_ONE_KNOWN_DW_AT (GNU_pubtypes, DW_AT_GNU_pubtypes) \
  DWARF_ONE_KNOWN_DW_AT (GNU_ranges_base, DW_AT_GNU_ranges_base) \
  DWARF_ONE_KNOWN_DW_AT (GNU_shared_locks_required, DW_AT_GNU_shared_locks_required) \
  DWARF_ONE_KNOWN_DW_AT (GNU_tail_call, DW_AT_GNU_tail_call) \
  DWARF_ONE_KNOWN_DW_AT (GNU_template_name, DW_AT_GNU_template_name) \
  DWARF_ONE_KNOWN_DW_AT (GNU_vector, DW_AT_GNU_vector) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_abstract_name, DW_AT_MIPS_abstract_name) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_allocatable_dopetype, DW_AT_MIPS_allocatable_dopetype) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_assumed_shape_dopetype, DW_AT_MIPS_assumed_shape_dopetype) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_assumed_size, DW_AT_MIPS_assumed_size) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_clone_origin, DW_AT_MIPS_clone_origin) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_epilog_begin, DW_AT_MIPS_epilog_begin) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_fde, DW_AT_MIPS_fde) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_has_inlines, DW_AT_MIPS_has_inlines) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_linkage_name, DW_AT_MIPS_linkage_name) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_loop_begin, DW_AT_MIPS_loop_begin) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_loop_unroll_factor, DW_AT_MIPS_loop_unroll_factor) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_ptr_dopetype, DW_AT_MIPS_ptr_dopetype) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_software_pipeline_depth, DW_AT_MIPS_software_pipeline_depth) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_stride, DW_AT_MIPS_stride) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_stride_byte, DW_AT_MIPS_stride_byte) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_stride_elem, DW_AT_MIPS_stride_elem) \
  DWARF_ONE_KNOWN_DW_AT (MIPS_tail_loop_begin, DW_AT_MIPS_tail_loop_begin) \
  DWARF_ONE_KNOWN_DW_AT (abstract_origin, DW_AT_abstract_origin) \
  DWARF_ONE_KNOWN_DW_AT (accessibility, DW_AT_accessibility) \
  DWARF_ONE_KNOWN_DW_AT (addr_base, DW_AT_addr_base) \
  DWARF_ONE_KNOWN_DW_AT (address_class, DW_AT_address_class) \
  DWARF_ONE_KNOWN_DW_AT (alignment, DW_AT_alignment) \
  DWARF_ONE_KNOWN_DW_AT (allocated, DW_AT_allocated) \
  DWARF_ONE_KNOWN_DW_AT (artificial, DW_AT_artificial) \
  DWARF_ONE_KNOWN_DW_AT (associated, DW_AT_associated) \
  DWARF_ONE_KNOWN_DW_AT (base_types, DW_AT_base_types) \
  DWARF_ONE_KNOWN_DW_AT (binary_scale, DW_AT_binary_scale) \
  DWARF_ONE_KNOWN_DW_AT (bit_offset, DW_AT_bit_offset) \
  DWARF_ONE_KNOWN_DW_AT (bit_size, DW_AT_bit_size) \
  DWARF_ONE_KNOWN_DW_AT (bit_stride, DW_AT_bit_stride) \
  DWARF_ONE_KNOWN_DW_AT (body_begin, DW_AT_body_begin) \
  DWARF_ONE_KNOWN_DW_AT (body_end, DW_AT_body_end) \
  DWARF_ONE_KNOWN_DW_AT (byte_size, DW_AT_byte_size) \
  DWARF_ONE_KNOWN_DW_AT (byte_stride, DW_AT_byte_stride) \
  DWARF_ONE_KNOWN_DW_AT (call_all_calls, DW_AT_call_all_calls) \
  DWARF_ONE_KNOWN_DW_AT (call_all_source_calls, DW_AT_call_all_source_calls) \
  DWARF_ONE_KNOWN_DW_AT (call_all_tail_calls, DW_AT_call_all_tail_calls) \
  DWARF_ONE_KNOWN_DW_AT (call_column, DW_AT_call_column) \
  DWARF_ONE_KNOWN_DW_AT (call_data_location, DW_AT_call_data_location) \
  DWARF_ONE_KNOWN_DW_AT (call_data_value, DW_AT_call_data_value) \
  DWARF_ONE_KNOWN_DW_AT (call_file, DW_AT_call_file) \
  DWARF_ONE_KNOWN_DW_AT (call_line, DW_AT_call_line) \
  DWARF_ONE_KNOWN_DW_AT (call_origin, DW_AT_call_origin) \
  DWARF_ONE_KNOWN_DW_AT (call_parameter, DW_AT_call_parameter) \
  DWARF_ONE_KNOWN_DW_AT (call_pc, DW_AT_call_pc) \
  DWARF_ONE_KNOWN_DW_AT (call_return_pc, DW_AT_call_return_pc) \
  DWARF_ONE_KNOWN_DW_AT (call_tail_call, DW_AT_call_tail_call) \
  DWARF_ONE_KNOWN_DW_AT (call_target, DW_AT_call_target) \
  DWARF_ONE_KNOWN_DW_AT (call_target_clobbered, DW_AT_call_target_clobbered) \
  DWARF_ONE_KNOWN_DW_AT (call_value, DW_AT_call_value) \
  DWARF_ONE_KNOWN_DW_AT (calling_convention, DW_AT_calling_convention) \
  DWARF_ONE_KNOWN_DW_AT (common_reference, DW_AT_common_reference) \
  DWARF_ONE_KNOWN_DW_AT (comp_dir, DW_AT_comp_dir) \
  DWARF_ONE_KNOWN_DW_AT (const_expr, DW_AT_const_expr) \
  DWARF_ONE_KNOWN_DW_AT (const_value, DW_AT_const_value) \
  DWARF_ONE_KNOWN_DW_AT (containing_type, DW_AT_containing_type) \
  DWARF_ONE_KNOWN_DW_AT (count, DW_AT_count) \
  DWARF_ONE_KNOWN_DW_AT (data_bit_offset, DW_AT_data_bit_offset) \
  DWARF_ONE_KNOWN_DW_AT (data_location, DW_AT_data_location) \
  DWARF_ONE_KNOWN_DW_AT (data_member_location, DW_AT_data_member_location) \
  DWARF_ONE_KNOWN_DW_AT (decimal_scale, DW_AT_decimal_scale) \
  DWARF_ONE_KNOWN_DW_AT (decimal_sign, DW_AT_decimal_sign) \
  DWARF_ONE_KNOWN_DW_AT (decl_column, DW_AT_decl_column) \
  DWARF_ONE_KNOWN_DW_AT (decl_file, DW_AT_decl_file) \
  DWARF_ONE_KNOWN_DW_AT (decl_line, DW_AT_decl_line) \
  DWARF_ONE_KNOWN_DW_AT (declaration, DW_AT_declaration) \
  DWARF_ONE_KNOWN_DW_AT (default_value, DW_AT_default_value) \
  DWARF_ONE_KNOWN_DW_AT (defaulted, DW_AT_defaulted) \
  DWARF_ONE_KNOWN_DW_AT (deleted, DW_AT_deleted) \
  DWARF_ONE_KNOWN_DW_AT (description, DW_AT_description) \
  DWARF_ONE_KNOWN_DW_AT (digit_count, DW_AT_digit_count) \
  DWARF_ONE_KNOWN_DW_AT (discr, DW_AT_discr) \
  DWARF_ONE_KNOWN_DW_AT (discr_list, DW_AT_discr_list) \
  DWARF_ONE_KNOWN_DW_AT (discr_value, DW_AT_discr_value) \
  DWARF_ONE_KNOWN_DW_AT (dwo_name, DW_AT_dwo_name) \
  DWARF_ONE_KNOWN_DW_AT (elemental, DW_AT_elemental) \
  DWARF_ONE_KNOWN_DW_AT (encoding, DW_AT_encoding) \
  DWARF_ONE_KNOWN_DW_AT (endianity, DW_AT_endianity) \
  DWARF_ONE_KNOWN_DW_AT (entry_pc, DW_AT_entry_pc) \
  DWARF_ONE_KNOWN_DW_AT (enum_class, DW_AT_enum_class) \
  DWARF_ONE_KNOWN_DW_AT (explicit, DW_AT_explicit) \
  DWARF_ONE_KNOWN_DW_AT (export_symbols, DW_AT_export_symbols) \
  DWARF_ONE_KNOWN_DW_AT (extension, DW_AT_extension) \
  DWARF_ONE_KNOWN_DW_AT (external, DW_AT_external) \
  DWARF_ONE_KNOWN_DW_AT (frame_base, DW_AT_frame_base) \
  DWARF_ONE_KNOWN_DW_AT (friend, DW_AT_friend) \
  DWARF_ONE_KNOWN_DW_AT (high_pc, DW_AT_high_pc) \
  DWARF_ONE_KNOWN_DW_AT (identifier_case, DW_AT_identifier_case) \
  DWARF_ONE_KNOWN_DW_AT (import, DW_AT_import) \
  DWARF_ONE_KNOWN_DW_AT (inline, DW_AT_inline) \
  DWARF_ONE_KNOWN_DW_AT (is_optional, DW_AT_is_optional) \
  DWARF_ONE_KNOWN_DW_AT (language, DW_AT_language) \
  DWARF_ONE_KNOWN_DW_AT (linkage_name, DW_AT_linkage_name) \
  DWARF_ONE_KNOWN_DW_AT (location, DW_AT_location) \
  DWARF_ONE_KNOWN_DW_AT (loclists_base, DW_AT_loclists_base) \
  DWARF_ONE_KNOWN_DW_AT (low_pc, DW_AT_low_pc) \
  DWARF_ONE_KNOWN_DW_AT (lower_bound, DW_AT_lower_bound) \
  DWARF_ONE_KNOWN_DW_AT (mac_info, DW_AT_mac_info) \
  DWARF_ONE_KNOWN_DW_AT (macro_info, DW_AT_macro_info) \
  DWARF_ONE_KNOWN_DW_AT (macros, DW_AT_macros) \
  DWARF_ONE_KNOWN_DW_AT (main_subprogram, DW_AT_main_subprogram) \
  DWARF_ONE_KNOWN_DW_AT (mutable, DW_AT_mutable) \
  DWARF_ONE_KNOWN_DW_AT (name, DW_AT_name) \
  DWARF_ONE_KNOWN_DW_AT (namelist_item, DW_AT_namelist_item) \
  DWARF_ONE_KNOWN_DW_AT (noreturn, DW_AT_noreturn) \
  DWARF_ONE_KNOWN_DW_AT (object_pointer, DW_AT_object_pointer) \
  DWARF_ONE_KNOWN_DW_AT (ordering, DW_AT_ordering) \
  DWARF_ONE_KNOWN_DW_AT (picture_string, DW_AT_picture_string) \
  DWARF_ONE_KNOWN_DW_AT (priority, DW_AT_priority) \
  DWARF_ONE_KNOWN_DW_AT (producer, DW_AT_producer) \
  DWARF_ONE_KNOWN_DW_AT (prototyped, DW_AT_prototyped) \
  DWARF_ONE_KNOWN_DW_AT (pure, DW_AT_pure) \
  DWARF_ONE_KNOWN_DW_AT (ranges, DW_AT_ranges) \
  DWARF_ONE_KNOWN_DW_AT (rank, DW_AT_rank) \
  DWARF_ONE_KNOWN_DW_AT (recursive, DW_AT_recursive) \
  DWARF_ONE_KNOWN_DW_AT (reference, DW_AT_reference) \
  DWARF_ONE_KNOWN_DW_AT (return_addr, DW_AT_return_addr) \
  DWARF_ONE_KNOWN_DW_AT (rnglists_base, DW_AT_rnglists_base) \
  DWARF_ONE_KNOWN_DW_AT (rvalue_reference, DW_AT_rvalue_reference) \
  DWARF_ONE_KNOWN_DW_AT (segment, DW_AT_segment) \
  DWARF_ONE_KNOWN_DW_AT (sf_names, DW_AT_sf_names) \
  DWARF_ONE_KNOWN_DW_AT (sibling, DW_AT_sibling) \
  DWARF_ONE_KNOWN_DW_AT (signature, DW_AT_signature) \
  DWARF_ONE_KNOWN_DW_AT (small, DW_AT_small) \
  DWARF_ONE_KNOWN_DW_AT (specification, DW_AT_specification) \
  DWARF_ONE_KNOWN_DW_AT (src_coords, DW_AT_src_coords) \
  DWARF_ONE_KNOWN_DW_AT (src_info, DW_AT_src_info) \
  DWARF_ONE_KNOWN_DW_AT (start_scope, DW_AT_start_scope) \
  DWARF_ONE_KNOWN_DW_AT (static_link, DW_AT_static_link) \
  DWARF_ONE_KNOWN_DW_AT (stmt_list, DW_AT_stmt_list) \
  DWARF_ONE_KNOWN_DW_AT (str_offsets_base, DW_AT_str_offsets_base) \
  DWARF_ONE_KNOWN_DW_AT (string_length, DW_AT_string_length) \
  DWARF_ONE_KNOWN_DW_AT (string_length_bit_size, DW_AT_string_length_bit_size) \
  DWARF_ONE_KNOWN_DW_AT (string_length_byte_size, DW_AT_string_length_byte_size) \
  DWARF_ONE_KNOWN_DW_AT (threads_scaled, DW_AT_threads_scaled) \
  DWARF_ONE_KNOWN_DW_AT (trampoline, DW_AT_trampoline) \
  DWARF_ONE_KNOWN_DW_AT (type, DW_AT_type) \
  DWARF_ONE_KNOWN_DW_AT (upper_bound, DW_AT_upper_bound) \
  DWARF_ONE_KNOWN_DW_AT (use_UTF8, DW_AT_use_UTF8) \
  DWARF_ONE_KNOWN_DW_AT (use_location, DW_AT_use_location) \
  DWARF_ONE_KNOWN_DW_AT (variable_parameter, DW_AT_variable_parameter) \
  DWARF_ONE_KNOWN_DW_AT (virtuality, DW_AT_virtuality) \
  DWARF_ONE_KNOWN_DW_AT (visibility, DW_AT_visibility) \
  DWARF_ONE_KNOWN_DW_AT (vtable_elem_location, DW_AT_vtable_elem_location) \
  /* End of DW_AT_*.  */

#define DWARF_ALL_KNOWN_DW_ATE \
  DWARF_ONE_KNOWN_DW_ATE (ASCII, DW_ATE_ASCII) \
  DWARF_ONE_KNOWN_DW_ATE (UCS, DW_ATE_UCS) \
  DWARF_ONE_KNOWN_DW_ATE (UTF, DW_ATE_UTF) \
  DWARF_ONE_KNOWN_DW_ATE (address, DW_ATE_address) \
  DWARF_ONE_KNOWN_DW_ATE (boolean, DW_ATE_boolean) \
  DWARF_ONE_KNOWN_DW_ATE (complex_float, DW_ATE_complex_float) \
  DWARF_ONE_KNOWN_DW_ATE (decimal_float, DW_ATE_decimal_float) \
  DWARF_ONE_KNOWN_DW_ATE (edited, DW_ATE_edited) \
  DWARF_ONE_KNOWN_DW_ATE (float, DW_ATE_float) \
  DWARF_ONE_KNOWN_DW_ATE (imaginary_float, DW_ATE_imaginary_float) \
  DWARF_ONE_KNOWN_DW_ATE (numeric_string, DW_ATE_numeric_string) \
  DWARF_ONE_KNOWN_DW_ATE (packed_decimal, DW_ATE_packed_decimal) \
  DWARF_ONE_KNOWN_DW_ATE (signed, DW_ATE_signed) \
  DWARF_ONE_KNOWN_DW_ATE (signed_char, DW_ATE_signed_char) \
  DWARF_ONE_KNOWN_DW_ATE (signed_fixed, DW_ATE_signed_fixed) \
  DWARF_ONE_KNOWN_DW_ATE (unsigned, DW_ATE_unsigned) \
  DWARF_ONE_KNOWN_DW_ATE (unsigned_char, DW_ATE_unsigned_char) \
  DWARF_ONE_KNOWN_DW_ATE (unsigned_fixed, DW_ATE_unsigned_fixed) \
  DWARF_ONE_KNOWN_DW_ATE (void, DW_ATE_void) \
  /* End of DW_ATE_*.  */

#define DWARF_ALL_KNOWN_DW_CC \
  DWARF_ONE_KNOWN_DW_CC (nocall, DW_CC_nocall) \
  DWARF_ONE_KNOWN_DW_CC (normal, DW_CC_normal) \
  DWARF_ONE_KNOWN_DW_CC (pass_by_reference, DW_CC_pass_by_reference) \
  DWARF_ONE_KNOWN_DW_CC (pass_by_value, DW_CC_pass_by_value) \
  DWARF_ONE_KNOWN_DW_CC (program, DW_CC_program) \
  /* End of DW_CC_*.  */

#define DWARF_ALL_KNOWN_DW_CFA \
  DWARF_ONE_KNOWN_DW_CFA (GNU_args_size, DW_CFA_GNU_args_size) \
  DWARF_ONE_KNOWN_DW_CFA (GNU_negative_offset_extended, DW_CFA_GNU_negative_offset_extended) \
  DWARF_ONE_KNOWN_DW_CFA (GNU_window_save, DW_CFA_GNU_window_save) \
  DWARF_ONE_KNOWN_DW_CFA (MIPS_advance_loc8, DW_CFA_MIPS_advance_loc8) \
  DWARF_ONE_KNOWN_DW_CFA (advance_loc, DW_CFA_advance_loc) \
  DWARF_ONE_KNOWN_DW_CFA (advance_loc1, DW_CFA_advance_loc1) \
  DWARF_ONE_KNOWN_DW_CFA (advance_loc2, DW_CFA_advance_loc2) \
  DWARF_ONE_KNOWN_DW_CFA (advance_loc4, DW_CFA_advance_loc4) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa, DW_CFA_def_cfa) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa_expression, DW_CFA_def_cfa_expression) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa_offset, DW_CFA_def_cfa_offset) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa_offset_sf, DW_CFA_def_cfa_offset_sf) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa_register, DW_CFA_def_cfa_register) \
  DWARF_ONE_KNOWN_DW_CFA (def_cfa_sf, DW_CFA_def_cfa_sf) \
  DWARF_ONE_KNOWN_DW_CFA (expression, DW_CFA_expression) \
  DWARF_ONE_KNOWN_DW_CFA (extended, DW_CFA_extended) \
  DWARF_ONE_KNOWN_DW_CFA (nop, DW_CFA_nop) \
  DWARF_ONE_KNOWN_DW_CFA (offset, DW_CFA_offset) \
  DWARF_ONE_KNOWN_DW_CFA (offset_extended, DW_CFA_offset_extended) \
  DWARF_ONE_KNOWN_DW_CFA (offset_extended_sf, DW_CFA_offset_extended_sf) \
  DWARF_ONE_KNOWN_DW_CFA (register, DW_CFA_register) \
  DWARF_ONE_KNOWN_DW_CFA (remember_state, DW_CFA_remember_state) \
  DWARF_ONE_KNOWN_DW_CFA (restore, DW_CFA_restore) \
  DWARF_ONE_KNOWN_DW_CFA (restore_extended, DW_CFA_restore_extended) \
  DWARF_ONE_KNOWN_DW_CFA (restore_state, DW_CFA_restore_state) \
  DWARF_ONE_KNOWN_DW_CFA (same_value, DW_CFA_same_value) \
  DWARF_ONE_KNOWN_DW_CFA (set_loc, DW_CFA_set_loc) \
  DWARF_ONE_KNOWN_DW_CFA (undefined, DW_CFA_undefined) \
  DWARF_ONE_KNOWN_DW_CFA (val_expression, DW_CFA_val_expression) \
  DWARF_ONE_KNOWN_DW_CFA (val_offset, DW_CFA_val_offset) \
  DWARF_ONE_KNOWN_DW_CFA (val_offset_sf, DW_CFA_val_offset_sf) \
  /* End of DW_CFA_*.  */

#define DWARF_ALL_KNOWN_DW_CHILDREN \
  DWARF_ONE_KNOWN_DW_CHILDREN (no, DW_CHILDREN_no) \
  DWARF_ONE_KNOWN_DW_CHILDREN (yes, DW_CHILDREN_yes) \
  /* End of DW_CHILDREN_*.  */

#define DWARF_ALL_KNOWN_DW_CIE_ID \
  DWARF_ONE_KNOWN_DW_CIE_ID (32, DW_CIE_ID_32) \
  DWARF_ONE_KNOWN_DW_CIE_ID (64, DW_CIE_ID_64) \
  /* End of DW_CIE_ID_*.  */

#define DWARF_ALL_KNOWN_DW_DEFAULTED \
  DWARF_ONE_KNOWN_DW_DEFAULTED (in_class, DW_DEFAULTED_in_class) \
  DWARF_ONE_KNOWN_DW_DEFAULTED (no, DW_DEFAULTED_no) \
  DWARF_ONE_KNOWN_DW_DEFAULTED (out_of_class, DW_DEFAULTED_out_of_class) \
  /* End of DW_DEFAULTED_*.  */

#define DWARF_ALL_KNOWN_DW_DS \
  DWARF_ONE_KNOWN_DW_DS (leading_overpunch, DW_DS_leading_overpunch) \
  DWARF_ONE_KNOWN_DW_DS (leading_separate, DW_DS_leading_separate) \
  DWARF_ONE_KNOWN_DW_DS (trailing_overpunch, DW_DS_trailing_overpunch) \
  DWARF_ONE_KNOWN_DW_DS (trailing_separate, DW_DS_trailing_separate) \
  DWARF_ONE_KNOWN_DW_DS (unsigned, DW_DS_unsigned) \
  /* End of DW_DS_*.  */

#define DWARF_ALL_KNOWN_DW_DSC \
  DWARF_ONE_KNOWN_DW_DSC (label, DW_DSC_label) \
  DWARF_ONE_KNOWN_DW_DSC (range, DW_DSC_range) \
  /* End of DW_DSC_*.  */

#define DWARF_ALL_KNOWN_DW_EH_PE \
  DWARF_ONE_KNOWN_DW_EH_PE (absptr, DW_EH_PE_absptr) \
  DWARF_ONE_KNOWN_DW_EH_PE (aligned, DW_EH_PE_aligned) \
  DWARF_ONE_KNOWN_DW_EH_PE (datarel, DW_EH_PE_datarel) \
  DWARF_ONE_KNOWN_DW_EH_PE (funcrel, DW_EH_PE_funcrel) \
  DWARF_ONE_KNOWN_DW_EH_PE (indirect, DW_EH_PE_indirect) \
  DWARF_ONE_KNOWN_DW_EH_PE (omit, DW_EH_PE_omit) \
  DWARF_ONE_KNOWN_DW_EH_PE (pcrel, DW_EH_PE_pcrel) \
  DWARF_ONE_KNOWN_DW_EH_PE (sdata2, DW_EH_PE_sdata2) \
  DWARF_ONE_KNOWN_DW_EH_PE (sdata4, DW_EH_PE_sdata4) \
  DWARF_ONE_KNOWN_DW_EH_PE (sdata8, DW_EH_PE_sdata8) \
  DWARF_ONE_KNOWN_DW_EH_PE (signed, DW_EH_PE_signed) \
  DWARF_ONE_KNOWN_DW_EH_PE (sleb128, DW_EH_PE_sleb128) \
  DWARF_ONE_KNOWN_DW_EH_PE (textrel, DW_EH_PE_textrel) \
  DWARF_ONE_KNOWN_DW_EH_PE (udata2, DW_EH_PE_udata2) \
  DWARF_ONE_KNOWN_DW_EH_PE (udata4, DW_EH_PE_udata4) \
  DWARF_ONE_KNOWN_DW_EH_PE (udata8, DW_EH_PE_udata8) \
  DWARF_ONE_KNOWN_DW_EH_PE (uleb128, DW_EH_PE_uleb128) \
  /* End of DW_EH_PE_*.  */

#define DWARF_ALL_KNOWN_DW_END \
  DWARF_ONE_KNOWN_DW_END (big, DW_END_big) \
  DWARF_ONE_KNOWN_DW_END (default, DW_END_default) \
  DWARF_ONE_KNOWN_DW_END (little, DW_END_little) \
  /* End of DW_END_*.  */

#define DWARF_ALL_KNOWN_DW_FORM \
  DWARF_ONE_KNOWN_DW_FORM (GNU_addr_index, DW_FORM_GNU_addr_index) \
  DWARF_ONE_KNOWN_DW_FORM (GNU_ref_alt, DW_FORM_GNU_ref_alt) \
  DWARF_ONE_KNOWN_DW_FORM (GNU_str_index, DW_FORM_GNU_str_index) \
  DWARF_ONE_KNOWN_DW_FORM (GNU_strp_alt, DW_FORM_GNU_strp_alt) \
  DWARF_ONE_KNOWN_DW_FORM (addr, DW_FORM_addr) \
  DWARF_ONE_KNOWN_DW_FORM (addrx, DW_FORM_addrx) \
  DWARF_ONE_KNOWN_DW_FORM (addrx1, DW_FORM_addrx1) \
  DWARF_ONE_KNOWN_DW_FORM (addrx2, DW_FORM_addrx2) \
  DWARF_ONE_KNOWN_DW_FORM (addrx3, DW_FORM_addrx3) \
  DWARF_ONE_KNOWN_DW_FORM (addrx4, DW_FORM_addrx4) \
  DWARF_ONE_KNOWN_DW_FORM (block, DW_FORM_block) \
  DWARF_ONE_KNOWN_DW_FORM (block1, DW_FORM_block1) \
  DWARF_ONE_KNOWN_DW_FORM (block2, DW_FORM_block2) \
  DWARF_ONE_KNOWN_DW_FORM (block4, DW_FORM_block4) \
  DWARF_ONE_KNOWN_DW_FORM (data1, DW_FORM_data1) \
  DWARF_ONE_KNOWN_DW_FORM (data16, DW_FORM_data16) \
  DWARF_ONE_KNOWN_DW_FORM (data2, DW_FORM_data2) \
  DWARF_ONE_KNOWN_DW_FORM (data4, DW_FORM_data4) \
  DWARF_ONE_KNOWN_DW_FORM (data8, DW_FORM_data8) \
  DWARF_ONE_KNOWN_DW_FORM (exprloc, DW_FORM_exprloc) \
  DWARF_ONE_KNOWN_DW_FORM (flag, DW_FORM_flag) \
  DWARF_ONE_KNOWN_DW_FORM (flag_present, DW_FORM_flag_present) \
  DWARF_ONE_KNOWN_DW_FORM (implicit_const, DW_FORM_implicit_const) \
  DWARF_ONE_KNOWN_DW_FORM (indirect, DW_FORM_indirect) \
  DWARF_ONE_KNOWN_DW_FORM (line_strp, DW_FORM_line_strp) \
  DWARF_ONE_KNOWN_DW_FORM (loclistx, DW_FORM_loclistx) \
  DWARF_ONE_KNOWN_DW_FORM (ref1, DW_FORM_ref1) \
  DWARF_ONE_KNOWN_DW_FORM (ref2, DW_FORM_ref2) \
  DWARF_ONE_KNOWN_DW_FORM (ref4, DW_FORM_ref4) \
  DWARF_ONE_KNOWN_DW_FORM (ref8, DW_FORM_ref8) \
  DWARF_ONE_KNOWN_DW_FORM (ref_addr, DW_FORM_ref_addr) \
  DWARF_ONE_KNOWN_DW_FORM (ref_sig8, DW_FORM_ref_sig8) \
  DWARF_ONE_KNOWN_DW_FORM (ref_sup4, DW_FORM_ref_sup4) \
  DWARF_ONE_KNOWN_DW_FORM (ref_sup8, DW_FORM_ref_sup8) \
  DWARF_ONE_KNOWN_DW_FORM (ref_udata, DW_FORM_ref_udata) \
  DWARF_ONE_KNOWN_DW_FORM (rnglistx, DW_FORM_rnglistx) \
  DWARF_ONE_KNOWN_DW_FORM (sdata, DW_FORM_sdata) \
  DWARF_ONE_KNOWN_DW_FORM (sec_offset, DW_FORM_sec_offset) \
  DWARF_ONE_KNOWN_DW_FORM (string, DW_FORM_string) \
  DWARF_ONE_KNOWN_DW_FORM (strp, DW_FORM_strp) \
  DWARF_ONE_KNOWN_DW_FORM (strp_sup, DW_FORM_strp_sup) \
  DWARF_ONE_KNOWN_DW_FORM (strx, DW_FORM_strx) \
  DWARF_ONE_KNOWN_DW_FORM (strx1, DW_FORM_strx1) \
  DWARF_ONE_KNOWN_DW_FORM (strx2, DW_FORM_strx2) \
  DWARF_ONE_KNOWN_DW_FORM (strx3, DW_FORM_strx3) \
  DWARF_ONE_KNOWN_DW_FORM (strx4, DW_FORM_strx4) \
  DWARF_ONE_KNOWN_DW_FORM (udata, DW_FORM_udata) \
  /* End of DW_FORM_*.  */

#define DWARF_ALL_KNOWN_DW_ID \
  DWARF_ONE_KNOWN_DW_ID (case_insensitive, DW_ID_case_insensitive) \
  DWARF_ONE_KNOWN_DW_ID (case_sensitive, DW_ID_case_sensitive) \
  DWARF_ONE_KNOWN_DW_ID (down_case, DW_ID_down_case) \
  DWARF_ONE_KNOWN_DW_ID (up_case, DW_ID_up_case) \
  /* End of DW_ID_*.  */

#define DWARF_ALL_KNOWN_DW_INL \
  DWARF_ONE_KNOWN_DW_INL (declared_inlined, DW_INL_declared_inlined) \
  DWARF_ONE_KNOWN_DW_INL (declared_not_inlined, DW_INL_declared_not_inlined) \
  DWARF_ONE_KNOWN_DW_INL (inlined, DW_INL_inlined) \
  DWARF_ONE_KNOWN_DW_INL (not_inlined, DW_INL_not_inlined) \
  /* End of DW_INL_*.  */

#define DWARF_ALL_KNOWN_DW_LANG \
  DWARF_ONE_KNOWN_DW_LANG (Ada83, DW_LANG_Ada83) \
  DWARF_ONE_KNOWN_DW_LANG (Ada95, DW_LANG_Ada95) \
  DWARF_ONE_KNOWN_DW_LANG (BLISS, DW_LANG_BLISS) \
  DWARF_ONE_KNOWN_DW_LANG (C, DW_LANG_C) \
  DWARF_ONE_KNOWN_DW_LANG (C11, DW_LANG_C11) \
  DWARF_ONE_KNOWN_DW_LANG (C89, DW_LANG_C89) \
  DWARF_ONE_KNOWN_DW_LANG (C99, DW_LANG_C99) \
  DWARF_ONE_KNOWN_DW_LANG (C_plus_plus, DW_LANG_C_plus_plus) \
  DWARF_ONE_KNOWN_DW_LANG (C_plus_plus_03, DW_LANG_C_plus_plus_03) \
  DWARF_ONE_KNOWN_DW_LANG (C_plus_plus_11, DW_LANG_C_plus_plus_11) \
  DWARF_ONE_KNOWN_DW_LANG (C_plus_plus_14, DW_LANG_C_plus_plus_14) \
  DWARF_ONE_KNOWN_DW_LANG (Cobol74, DW_LANG_Cobol74) \
  DWARF_ONE_KNOWN_DW_LANG (Cobol85, DW_LANG_Cobol85) \
  DWARF_ONE_KNOWN_DW_LANG (D, DW_LANG_D) \
  DWARF_ONE_KNOWN_DW_LANG (Dylan, DW_LANG_Dylan) \
  DWARF_ONE_KNOWN_DW_LANG (Fortran03, DW_LANG_Fortran03) \
  DWARF_ONE_KNOWN_DW_LANG (Fortran08, DW_LANG_Fortran08) \
  DWARF_ONE_KNOWN_DW_LANG (Fortran77, DW_LANG_Fortran77) \
  DWARF_ONE_KNOWN_DW_LANG (Fortran90, DW_LANG_Fortran90) \
  DWARF_ONE_KNOWN_DW_LANG (Fortran95, DW_LANG_Fortran95) \
  DWARF_ONE_KNOWN_DW_LANG (Go, DW_LANG_Go) \
  DWARF_ONE_KNOWN_DW_LANG (Haskell, DW_LANG_Haskell) \
  DWARF_ONE_KNOWN_DW_LANG (Java, DW_LANG_Java) \
  DWARF_ONE_KNOWN_DW_LANG (Julia, DW_LANG_Julia) \
  DWARF_ONE_KNOWN_DW_LANG (Mips_Assembler, DW_LANG_Mips_Assembler) \
  DWARF_ONE_KNOWN_DW_LANG (Modula2, DW_LANG_Modula2) \
  DWARF_ONE_KNOWN_DW_LANG (Modula3, DW_LANG_Modula3) \
  DWARF_ONE_KNOWN_DW_LANG (OCaml, DW_LANG_OCaml) \
  DWARF_ONE_KNOWN_DW_LANG (ObjC, DW_LANG_ObjC) \
  DWARF_ONE_KNOWN_DW_LANG (ObjC_plus_plus, DW_LANG_ObjC_plus_plus) \
  DWARF_ONE_KNOWN_DW_LANG (OpenCL, DW_LANG_OpenCL) \
  DWARF_ONE_KNOWN_DW_LANG (PLI, DW_LANG_PLI) \
  DWARF_ONE_KNOWN_DW_LANG (Pascal83, DW_LANG_Pascal83) \
  DWARF_ONE_KNOWN_DW_LANG (Python, DW_LANG_Python) \
  DWARF_ONE_KNOWN_DW_LANG (RenderScript, DW_LANG_RenderScript) \
  DWARF_ONE_KNOWN_DW_LANG (Rust, DW_LANG_Rust) \
  DWARF_ONE_KNOWN_DW_LANG (Swift, DW_LANG_Swift) \
  DWARF_ONE_KNOWN_DW_LANG (UPC, DW_LANG_UPC) \
  /* End of DW_LANG_*.  */

#define DWARF_ALL_KNOWN_DW_LLE \
  DWARF_ONE_KNOWN_DW_LLE (base_address, DW_LLE_base_address) \
  DWARF_ONE_KNOWN_DW_LLE (base_addressx, DW_LLE_base_addressx) \
  DWARF_ONE_KNOWN_DW_LLE (default_location, DW_LLE_default_location) \
  DWARF_ONE_KNOWN_DW_LLE (end_of_list, DW_LLE_end_of_list) \
  DWARF_ONE_KNOWN_DW_LLE (offset_pair, DW_LLE_offset_pair) \
  DWARF_ONE_KNOWN_DW_LLE (start_end, DW_LLE_start_end) \
  DWARF_ONE_KNOWN_DW_LLE (start_length, DW_LLE_start_length) \
  DWARF_ONE_KNOWN_DW_LLE (startx_endx, DW_LLE_startx_endx) \
  DWARF_ONE_KNOWN_DW_LLE (startx_length, DW_LLE_startx_length) \
  /* End of DW_LLE_*.  */

#define DWARF_ALL_KNOWN_DW_LLE_GNU \
  DWARF_ONE_KNOWN_DW_LLE_GNU (base_address_selection_entry, DW_LLE_GNU_base_address_selection_entry) \
  DWARF_ONE_KNOWN_DW_LLE_GNU (end_of_list_entry, DW_LLE_GNU_end_of_list_entry) \
  DWARF_ONE_KNOWN_DW_LLE_GNU (start_end_entry, DW_LLE_GNU_start_end_entry) \
  DWARF_ONE_KNOWN_DW_LLE_GNU (start_length_entry, DW_LLE_GNU_start_length_entry) \
  /* End of DW_LLE_GNU_*.  */

#define DWARF_ALL_KNOWN_DW_LNCT \
  DWARF_ONE_KNOWN_DW_LNCT (MD5, DW_LNCT_MD5) \
  DWARF_ONE_KNOWN_DW_LNCT (directory_index, DW_LNCT_directory_index) \
  DWARF_ONE_KNOWN_DW_LNCT (path, DW_LNCT_path) \
  DWARF_ONE_KNOWN_DW_LNCT (size, DW_LNCT_size) \
  DWARF_ONE_KNOWN_DW_LNCT (timestamp, DW_LNCT_timestamp) \
  /* End of DW_LNCT_*.  */

#define DWARF_ALL_KNOWN_DW_LNE \
  DWARF_ONE_KNOWN_DW_LNE (define_file, DW_LNE_define_file) \
  DWARF_ONE_KNOWN_DW_LNE (end_sequence, DW_LNE_end_sequence) \
  DWARF_ONE_KNOWN_DW_LNE (set_address, DW_LNE_set_address) \
  DWARF_ONE_KNOWN_DW_LNE (set_discriminator, DW_LNE_set_discriminator) \
  /* End of DW_LNE_*.  */

#define DWARF_ALL_KNOWN_DW_LNS \
  DWARF_ONE_KNOWN_DW_LNS (advance_line, DW_LNS_advance_line) \
  DWARF_ONE_KNOWN_DW_LNS (advance_pc, DW_LNS_advance_pc) \
  DWARF_ONE_KNOWN_DW_LNS (const_add_pc, DW_LNS_const_add_pc) \
  DWARF_ONE_KNOWN_DW_LNS (copy, DW_LNS_copy) \
  DWARF_ONE_KNOWN_DW_LNS (fixed_advance_pc, DW_LNS_fixed_advance_pc) \
  DWARF_ONE_KNOWN_DW_LNS (negate_stmt, DW_LNS_negate_stmt) \
  DWARF_ONE_KNOWN_DW_LNS (set_basic_block, DW_LNS_set_basic_block) \
  DWARF_ONE_KNOWN_DW_LNS (set_column, DW_LNS_set_column) \
  DWARF_ONE_KNOWN_DW_LNS (set_epilogue_begin, DW_LNS_set_epilogue_begin) \
  DWARF_ONE_KNOWN_DW_LNS (set_file, DW_LNS_set_file) \
  DWARF_ONE_KNOWN_DW_LNS (set_isa, DW_LNS_set_isa) \
  DWARF_ONE_KNOWN_DW_LNS (set_prologue_end, DW_LNS_set_prologue_end) \
  /* End of DW_LNS_*.  */

#define DWARF_ALL_KNOWN_DW_MACINFO \
  DWARF_ONE_KNOWN_DW_MACINFO (define, DW_MACINFO_define) \
  DWARF_ONE_KNOWN_DW_MACINFO (end_file, DW_MACINFO_end_file) \
  DWARF_ONE_KNOWN_DW_MACINFO (start_file, DW_MACINFO_start_file) \
  DWARF_ONE_KNOWN_DW_MACINFO (undef, DW_MACINFO_undef) \
  DWARF_ONE_KNOWN_DW_MACINFO (vendor_ext, DW_MACINFO_vendor_ext) \
  /* End of DW_MACINFO_*.  */

#define DWARF_ALL_KNOWN_DW_MACRO \
  DWARF_ONE_KNOWN_DW_MACRO (define, DW_MACRO_define) \
  DWARF_ONE_KNOWN_DW_MACRO (define_strp, DW_MACRO_define_strp) \
  DWARF_ONE_KNOWN_DW_MACRO (define_strx, DW_MACRO_define_strx) \
  DWARF_ONE_KNOWN_DW_MACRO (define_sup, DW_MACRO_define_sup) \
  DWARF_ONE_KNOWN_DW_MACRO (end_file, DW_MACRO_end_file) \
  DWARF_ONE_KNOWN_DW_MACRO (import, DW_MACRO_import) \
  DWARF_ONE_KNOWN_DW_MACRO (import_sup, DW_MACRO_import_sup) \
  DWARF_ONE_KNOWN_DW_MACRO (start_file, DW_MACRO_start_file) \
  DWARF_ONE_KNOWN_DW_MACRO (undef, DW_MACRO_undef) \
  DWARF_ONE_KNOWN_DW_MACRO (undef_strp, DW_MACRO_undef_strp) \
  DWARF_ONE_KNOWN_DW_MACRO (undef_strx, DW_MACRO_undef_strx) \
  DWARF_ONE_KNOWN_DW_MACRO (undef_sup, DW_MACRO_undef_sup) \
  /* End of DW_MACRO_*.  */

#define DWARF_ALL_KNOWN_DW_OP \
  DWARF_ONE_KNOWN_DW_OP (GNU_addr_index, DW_OP_GNU_addr_index) \
  DWARF_ONE_KNOWN_DW_OP (GNU_const_index, DW_OP_GNU_const_index) \
  DWARF_ONE_KNOWN_DW_OP (GNU_const_type, DW_OP_GNU_const_type) \
  DWARF_ONE_KNOWN_DW_OP (GNU_convert, DW_OP_GNU_convert) \
  DWARF_ONE_KNOWN_DW_OP (GNU_deref_type, DW_OP_GNU_deref_type) \
  DWARF_ONE_KNOWN_DW_OP (GNU_encoded_addr, DW_OP_GNU_encoded_addr) \
  DWARF_ONE_KNOWN_DW_OP (GNU_entry_value, DW_OP_GNU_entry_value) \
  DWARF_ONE_KNOWN_DW_OP (GNU_implicit_pointer, DW_OP_GNU_implicit_pointer) \
  DWARF_ONE_KNOWN_DW_OP (GNU_parameter_ref, DW_OP_GNU_parameter_ref) \
  DWARF_ONE_KNOWN_DW_OP (GNU_push_tls_address, DW_OP_GNU_push_tls_address) \
  DWARF_ONE_KNOWN_DW_OP (GNU_regval_type, DW_OP_GNU_regval_type) \
  DWARF_ONE_KNOWN_DW_OP (GNU_reinterpret, DW_OP_GNU_reinterpret) \
  DWARF_ONE_KNOWN_DW_OP (GNU_uninit, DW_OP_GNU_uninit) \
  DWARF_ONE_KNOWN_DW_OP (GNU_variable_value, DW_OP_GNU_variable_value) \
  DWARF_ONE_KNOWN_DW_OP (abs, DW_OP_abs) \
  DWARF_ONE_KNOWN_DW_OP (addr, DW_OP_addr) \
  DWARF_ONE_KNOWN_DW_OP (addrx, DW_OP_addrx) \
  DWARF_ONE_KNOWN_DW_OP (and, DW_OP_and) \
  DWARF_ONE_KNOWN_DW_OP (bit_piece, DW_OP_bit_piece) \
  DWARF_ONE_KNOWN_DW_OP (bra, DW_OP_bra) \
  DWARF_ONE_KNOWN_DW_OP (breg0, DW_OP_breg0) \
  DWARF_ONE_KNOWN_DW_OP (breg1, DW_OP_breg1) \
  DWARF_ONE_KNOWN_DW_OP (breg10, DW_OP_breg10) \
  DWARF_ONE_KNOWN_DW_OP (breg11, DW_OP_breg11) \
  DWARF_ONE_KNOWN_DW_OP (breg12, DW_OP_breg12) \
  DWARF_ONE_KNOWN_DW_OP (breg13, DW_OP_breg13) \
  DWARF_ONE_KNOWN_DW_OP (breg14, DW_OP_breg14) \
  DWARF_ONE_KNOWN_DW_OP (breg15, DW_OP_breg15) \
  DWARF_ONE_KNOWN_DW_OP (breg16, DW_OP_breg16) \
  DWARF_ONE_KNOWN_DW_OP (breg17, DW_OP_breg17) \
  DWARF_ONE_KNOWN_DW_OP (breg18, DW_OP_breg18) \
  DWARF_ONE_KNOWN_DW_OP (breg19, DW_OP_breg19) \
  DWARF_ONE_KNOWN_DW_OP (breg2, DW_OP_breg2) \
  DWARF_ONE_KNOWN_DW_OP (breg20, DW_OP_breg20) \
  DWARF_ONE_KNOWN_DW_OP (breg21, DW_OP_breg21) \
  DWARF_ONE_KNOWN_DW_OP (breg22, DW_OP_breg22) \
  DWARF_ONE_KNOWN_DW_OP (breg23, DW_OP_breg23) \
  DWARF_ONE_KNOWN_DW_OP (breg24, DW_OP_breg24) \
  DWARF_ONE_KNOWN_DW_OP (breg25, DW_OP_breg25) \
  DWARF_ONE_KNOWN_DW_OP (breg26, DW_OP_breg26) \
  DWARF_ONE_KNOWN_DW_OP (breg27, DW_OP_breg27) \
  DWARF_ONE_KNOWN_DW_OP (breg28, DW_OP_breg28) \
  DWARF_ONE_KNOWN_DW_OP (breg29, DW_OP_breg29) \
  DWARF_ONE_KNOWN_DW_OP (breg3, DW_OP_breg3) \
  DWARF_ONE_KNOWN_DW_OP (breg30, DW_OP_breg30) \
  DWARF_ONE_KNOWN_DW_OP (breg31, DW_OP_breg31) \
  DWARF_ONE_KNOWN_DW_OP (breg4, DW_OP_breg4) \
  DWARF_ONE_KNOWN_DW_OP (breg5, DW_OP_breg5) \
  DWARF_ONE_KNOWN_DW_OP (breg6, DW_OP_breg6) \
  DWARF_ONE_KNOWN_DW_OP (breg7, DW_OP_breg7) \
  DWARF_ONE_KNOWN_DW_OP (breg8, DW_OP_breg8) \
  DWARF_ONE_KNOWN_DW_OP (breg9, DW_OP_breg9) \
  DWARF_ONE_KNOWN_DW_OP (bregx, DW_OP_bregx) \
  DWARF_ONE_KNOWN_DW_OP (call2, DW_OP_call2) \
  DWARF_ONE_KNOWN_DW_OP (call4, DW_OP_call4) \
  DWARF_ONE_KNOWN_DW_OP (call_frame_cfa, DW_OP_call_frame_cfa) \
  DWARF_ONE_KNOWN_DW_OP (call_ref, DW_OP_call_ref) \
  DWARF_ONE_KNOWN_DW_OP (const1s, DW_OP_const1s) \
  DWARF_ONE_KNOWN_DW_OP (const1u, DW_OP_const1u) \
  DWARF_ONE_KNOWN_DW_OP (const2s, DW_OP_const2s) \
  DWARF_ONE_KNOWN_DW_OP (const2u, DW_OP_const2u) \
  DWARF_ONE_KNOWN_DW_OP (const4s, DW_OP_const4s) \
  DWARF_ONE_KNOWN_DW_OP (const4u, DW_OP_const4u) \
  DWARF_ONE_KNOWN_DW_OP (const8s, DW_OP_const8s) \
  DWARF_ONE_KNOWN_DW_OP (const8u, DW_OP_const8u) \
  DWARF_ONE_KNOWN_DW_OP (const_type, DW_OP_const_type) \
  DWARF_ONE_KNOWN_DW_OP (consts, DW_OP_consts) \
  DWARF_ONE_KNOWN_DW_OP (constu, DW_OP_constu) \
  DWARF_ONE_KNOWN_DW_OP (constx, DW_OP_constx) \
  DWARF_ONE_KNOWN_DW_OP (convert, DW_OP_convert) \
  DWARF_ONE_KNOWN_DW_OP (deref, DW_OP_deref) \
  DWARF_ONE_KNOWN_DW_OP (deref_size, DW_OP_deref_size) \
  DWARF_ONE_KNOWN_DW_OP (deref_type, DW_OP_deref_type) \
  DWARF_ONE_KNOWN_DW_OP (div, DW_OP_div) \
  DWARF_ONE_KNOWN_DW_OP (drop, DW_OP_drop) \
  DWARF_ONE_KNOWN_DW_OP (dup, DW_OP_dup) \
  DWARF_ONE_KNOWN_DW_OP (entry_value, DW_OP_entry_value) \
  DWARF_ONE_KNOWN_DW_OP (eq, DW_OP_eq) \
  DWARF_ONE_KNOWN_DW_OP (fbreg, DW_OP_fbreg) \
  DWARF_ONE_KNOWN_DW_OP (form_tls_address, DW_OP_form_tls_address) \
  DWARF_ONE_KNOWN_DW_OP (ge, DW_OP_ge) \
  DWARF_ONE_KNOWN_DW_OP (gt, DW_OP_gt) \
  DWARF_ONE_KNOWN_DW_OP (implicit_pointer, DW_OP_implicit_pointer) \
  DWARF_ONE_KNOWN_DW_OP (implicit_value, DW_OP_implicit_value) \
  DWARF_ONE_KNOWN_DW_OP (le, DW_OP_le) \
  DWARF_ONE_KNOWN_DW_OP (lit0, DW_OP_lit0) \
  DWARF_ONE_KNOWN_DW_OP (lit1, DW_OP_lit1) \
  DWARF_ONE_KNOWN_DW_OP (lit10, DW_OP_lit10) \
  DWARF_ONE_KNOWN_DW_OP (lit11, DW_OP_lit11) \
  DWARF_ONE_KNOWN_DW_OP (lit12, DW_OP_lit12) \
  DWARF_ONE_KNOWN_DW_OP (lit13, DW_OP_lit13) \
  DWARF_ONE_KNOWN_DW_OP (lit14, DW_OP_lit14) \
  DWARF_ONE_KNOWN_DW_OP (lit15, DW_OP_lit15) \
  DWARF_ONE_KNOWN_DW_OP (lit16, DW_OP_lit16) \
  DWARF_ONE_KNOWN_DW_OP (lit17, DW_OP_lit17) \
  DWARF_ONE_KNOWN_DW_OP (lit18, DW_OP_lit18) \
  DWARF_ONE_KNOWN_DW_OP (lit19, DW_OP_lit19) \
  DWARF_ONE_KNOWN_DW_OP (lit2, DW_OP_lit2) \
  DWARF_ONE_KNOWN_DW_OP (lit20, DW_OP_lit20) \
  DWARF_ONE_KNOWN_DW_OP (lit21, DW_OP_lit21) \
  DWARF_ONE_KNOWN_DW_OP (lit22, DW_OP_lit22) \
  DWARF_ONE_KNOWN_DW_OP (lit23, DW_OP_lit23) \
  DWARF_ONE_KNOWN_DW_OP (lit24, DW_OP_lit24) \
  DWARF_ONE_KNOWN_DW_OP (lit25, DW_OP_lit25) \
  DWARF_ONE_KNOWN_DW_OP (lit26, DW_OP_lit26) \
  DWARF_ONE_KNOWN_DW_OP (lit27, DW_OP_lit27) \
  DWARF_ONE_KNOWN_DW_OP (lit28, DW_OP_lit28) \
  DWARF_ONE_KNOWN_DW_OP (lit29, DW_OP_lit29) \
  DWARF_ONE_KNOWN_DW_OP (lit3, DW_OP_lit3) \
  DWARF_ONE_KNOWN_DW_OP (lit30, DW_OP_lit30) \
  DWARF_ONE_KNOWN_DW_OP (lit31, DW_OP_lit31) \
  DWARF_ONE_KNOWN_DW_OP (lit4, DW_OP_lit4) \
  DWARF_ONE_KNOWN_DW_OP (lit5, DW_OP_lit5) \
  DWARF_ONE_KNOWN_DW_OP (lit6, DW_OP_lit6) \
  DWARF_ONE_KNOWN_DW_OP (lit7, DW_OP_lit7) \
  DWARF_ONE_KNOWN_DW_OP (lit8, DW_OP_lit8) \
  DWARF_ONE_KNOWN_DW_OP (lit9, DW_OP_lit9) \
  DWARF_ONE_KNOWN_DW_OP (lt, DW_OP_lt) \
  DWARF_ONE_KNOWN_DW_OP (minus, DW_OP_minus) \
  DWARF_ONE_KNOWN_DW_OP (mod, DW_OP_mod) \
  DWARF_ONE_KNOWN_DW_OP (mul, DW_OP_mul) \
  DWARF_ONE_KNOWN_DW_OP (ne, DW_OP_ne) \
  DWARF_ONE_KNOWN_DW_OP (neg, DW_OP_neg) \
  DWARF_ONE_KNOWN_DW_OP (nop, DW_OP_nop) \
  DWARF_ONE_KNOWN_DW_OP (not, DW_OP_not) \
  DWARF_ONE_KNOWN_DW_OP (or, DW_OP_or) \
  DWARF_ONE_KNOWN_DW_OP (over, DW_OP_over) \
  DWARF_ONE_KNOWN_DW_OP (pick, DW_OP_pick) \
  DWARF_ONE_KNOWN_DW_OP (piece, DW_OP_piece) \
  DWARF_ONE_KNOWN_DW_OP (plus, DW_OP_plus) \
  DWARF_ONE_KNOWN_DW_OP (plus_uconst, DW_OP_plus_uconst) \
  DWARF_ONE_KNOWN_DW_OP (push_object_address, DW_OP_push_object_address) \
  DWARF_ONE_KNOWN_DW_OP (reg0, DW_OP_reg0) \
  DWARF_ONE_KNOWN_DW_OP (reg1, DW_OP_reg1) \
  DWARF_ONE_KNOWN_DW_OP (reg10, DW_OP_reg10) \
  DWARF_ONE_KNOWN_DW_OP (reg11, DW_OP_reg11) \
  DWARF_ONE_KNOWN_DW_OP (reg12, DW_OP_reg12) \
  DWARF_ONE_KNOWN_DW_OP (reg13, DW_OP_reg13) \
  DWARF_ONE_KNOWN_DW_OP (reg14, DW_OP_reg14) \
  DWARF_ONE_KNOWN_DW_OP (reg15, DW_OP_reg15) \
  DWARF_ONE_KNOWN_DW_OP (reg16, DW_OP_reg16) \
  DWARF_ONE_KNOWN_DW_OP (reg17, DW_OP_reg17) \
  DWARF_ONE_KNOWN_DW_OP (reg18, DW_OP_reg18) \
  DWARF_ONE_KNOWN_DW_OP (reg19, DW_OP_reg19) \
  DWARF_ONE_KNOWN_DW_OP (reg2, DW_OP_reg2) \
  DWARF_ONE_KNOWN_DW_OP (reg20, DW_OP_reg20) \
  DWARF_ONE_KNOWN_DW_OP (reg21, DW_OP_reg21) \
  DWARF_ONE_KNOWN_DW_OP (reg22, DW_OP_reg22) \
  DWARF_ONE_KNOWN_DW_OP (reg23, DW_OP_reg23) \
  DWARF_ONE_KNOWN_DW_OP (reg24, DW_OP_reg24) \
  DWARF_ONE_KNOWN_DW_OP (reg25, DW_OP_reg25) \
  DWARF_ONE_KNOWN_DW_OP (reg26, DW_OP_reg26) \
  DWARF_ONE_KNOWN_DW_OP (reg27, DW_OP_reg27) \
  DWARF_ONE_KNOWN_DW_OP (reg28, DW_OP_reg28) \
  DWARF_ONE_KNOWN_DW_OP (reg29, DW_OP_reg29) \
  DWARF_ONE_KNOWN_DW_OP (reg3, DW_OP_reg3) \
  DWARF_ONE_KNOWN_DW_OP (reg30, DW_OP_reg30) \
  DWARF_ONE_KNOWN_DW_OP (reg31, DW_OP_reg31) \
  DWARF_ONE_KNOWN_DW_OP (reg4, DW_OP_reg4) \
  DWARF_ONE_KNOWN_DW_OP (reg5, DW_OP_reg5) \
  DWARF_ONE_KNOWN_DW_OP (reg6, DW_OP_reg6) \
  DWARF_ONE_KNOWN_DW_OP (reg7, DW_OP_reg7) \
  DWARF_ONE_KNOWN_DW_OP (reg8, DW_OP_reg8) \
  DWARF_ONE_KNOWN_DW_OP (reg9, DW_OP_reg9) \
  DWARF_ONE_KNOWN_DW_OP (regval_type, DW_OP_regval_type) \
  DWARF_ONE_KNOWN_DW_OP (regx, DW_OP_regx) \
  DWARF_ONE_KNOWN_DW_OP (reinterpret, DW_OP_reinterpret) \
  DWARF_ONE_KNOWN_DW_OP (rot, DW_OP_rot) \
  DWARF_ONE_KNOWN_DW_OP (shl, DW_OP_shl) \
  DWARF_ONE_KNOWN_DW_OP (shr, DW_OP_shr) \
  DWARF_ONE_KNOWN_DW_OP (shra, DW_OP_shra) \
  DWARF_ONE_KNOWN_DW_OP (skip, DW_OP_skip) \
  DWARF_ONE_KNOWN_DW_OP (stack_value, DW_OP_stack_value) \
  DWARF_ONE_KNOWN_DW_OP (swap, DW_OP_swap) \
  DWARF_ONE_KNOWN_DW_OP (xderef, DW_OP_xderef) \
  DWARF_ONE_KNOWN_DW_OP (xderef_size, DW_OP_xderef_size) \
  DWARF_ONE_KNOWN_DW_OP (xderef_type, DW_OP_xderef_type) \
  DWARF_ONE_KNOWN_DW_OP (xor, DW_OP_xor) \
  /* End of DW_OP_*.  */

#define DWARF_ALL_KNOWN_DW_ORD \
  DWARF_ONE_KNOWN_DW_ORD (col_major, DW_ORD_col_major) \
  DWARF_ONE_KNOWN_DW_ORD (row_major, DW_ORD_row_major) \
  /* End of DW_ORD_*.  */

#define DWARF_ALL_KNOWN_DW_RLE \
  DWARF_ONE_KNOWN_DW_RLE (base_address, DW_RLE_base_address) \
  DWARF_ONE_KNOWN_DW_RLE (base_addressx, DW_RLE_base_addressx) \
  DWARF_ONE_KNOWN_DW_RLE (end_of_list, DW_RLE_end_of_list) \
  DWARF_ONE_KNOWN_DW_RLE (offset_pair, DW_RLE_offset_pair) \
  DWARF_ONE_KNOWN_DW_RLE (start_end, DW_RLE_start_end) \
  DWARF_ONE_KNOWN_DW_RLE (start_length, DW_RLE_start_length) \
  DWARF_ONE_KNOWN_DW_RLE (startx_endx, DW_RLE_startx_endx) \
  DWARF_ONE_KNOWN_DW_RLE (startx_length, DW_RLE_startx_length) \
  /* End of DW_RLE_*.  */

#define DWARF_ALL_KNOWN_DW_TAG \
  DWARF_ONE_KNOWN_DW_TAG (GNU_BINCL, DW_TAG_GNU_BINCL) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_EINCL, DW_TAG_GNU_EINCL) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_call_site, DW_TAG_GNU_call_site) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_call_site_parameter, DW_TAG_GNU_call_site_parameter) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_formal_parameter_pack, DW_TAG_GNU_formal_parameter_pack) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_template_parameter_pack, DW_TAG_GNU_template_parameter_pack) \
  DWARF_ONE_KNOWN_DW_TAG (GNU_template_template_param, DW_TAG_GNU_template_template_param) \
  DWARF_ONE_KNOWN_DW_TAG (MIPS_loop, DW_TAG_MIPS_loop) \
  DWARF_ONE_KNOWN_DW_TAG (access_declaration, DW_TAG_access_declaration) \
  DWARF_ONE_KNOWN_DW_TAG (array_type, DW_TAG_array_type) \
  DWARF_ONE_KNOWN_DW_TAG (atomic_type, DW_TAG_atomic_type) \
  DWARF_ONE_KNOWN_DW_TAG (base_type, DW_TAG_base_type) \
  DWARF_ONE_KNOWN_DW_TAG (call_site, DW_TAG_call_site) \
  DWARF_ONE_KNOWN_DW_TAG (call_site_parameter, DW_TAG_call_site_parameter) \
  DWARF_ONE_KNOWN_DW_TAG (catch_block, DW_TAG_catch_block) \
  DWARF_ONE_KNOWN_DW_TAG (class_template, DW_TAG_class_template) \
  DWARF_ONE_KNOWN_DW_TAG (class_type, DW_TAG_class_type) \
  DWARF_ONE_KNOWN_DW_TAG (coarray_type, DW_TAG_coarray_type) \
  DWARF_ONE_KNOWN_DW_TAG (common_block, DW_TAG_common_block) \
  DWARF_ONE_KNOWN_DW_TAG (common_inclusion, DW_TAG_common_inclusion) \
  DWARF_ONE_KNOWN_DW_TAG (compile_unit, DW_TAG_compile_unit) \
  DWARF_ONE_KNOWN_DW_TAG (condition, DW_TAG_condition) \
  DWARF_ONE_KNOWN_DW_TAG (const_type, DW_TAG_const_type) \
  DWARF_ONE_KNOWN_DW_TAG (constant, DW_TAG_constant) \
  DWARF_ONE_KNOWN_DW_TAG (dwarf_procedure, DW_TAG_dwarf_procedure) \
  DWARF_ONE_KNOWN_DW_TAG (dynamic_type, DW_TAG_dynamic_type) \
  DWARF_ONE_KNOWN_DW_TAG (entry_point, DW_TAG_entry_point) \
  DWARF_ONE_KNOWN_DW_TAG (enumeration_type, DW_TAG_enumeration_type) \
  DWARF_ONE_KNOWN_DW_TAG (enumerator, DW_TAG_enumerator) \
  DWARF_ONE_KNOWN_DW_TAG (file_type, DW_TAG_file_type) \
  DWARF_ONE_KNOWN_DW_TAG (formal_parameter, DW_TAG_formal_parameter) \
  DWARF_ONE_KNOWN_DW_TAG (format_label, DW_TAG_format_label) \
  DWARF_ONE_KNOWN_DW_TAG (friend, DW_TAG_friend) \
  DWARF_ONE_KNOWN_DW_TAG (function_template, DW_TAG_function_template) \
  DWARF_ONE_KNOWN_DW_TAG (generic_subrange, DW_TAG_generic_subrange) \
  DWARF_ONE_KNOWN_DW_TAG (immutable_type, DW_TAG_immutable_type) \
  DWARF_ONE_KNOWN_DW_TAG (imported_declaration, DW_TAG_imported_declaration) \
  DWARF_ONE_KNOWN_DW_TAG (imported_module, DW_TAG_imported_module) \
  DWARF_ONE_KNOWN_DW_TAG (imported_unit, DW_TAG_imported_unit) \
  DWARF_ONE_KNOWN_DW_TAG (inheritance, DW_TAG_inheritance) \
  DWARF_ONE_KNOWN_DW_TAG (inlined_subroutine, DW_TAG_inlined_subroutine) \
  DWARF_ONE_KNOWN_DW_TAG (interface_type, DW_TAG_interface_type) \
  DWARF_ONE_KNOWN_DW_TAG (label, DW_TAG_label) \
  DWARF_ONE_KNOWN_DW_TAG (lexical_block, DW_TAG_lexical_block) \
  DWARF_ONE_KNOWN_DW_TAG (member, DW_TAG_member) \
  DWARF_ONE_KNOWN_DW_TAG (module, DW_TAG_module) \
  DWARF_ONE_KNOWN_DW_TAG (namelist, DW_TAG_namelist) \
  DWARF_ONE_KNOWN_DW_TAG (namelist_item, DW_TAG_namelist_item) \
  DWARF_ONE_KNOWN_DW_TAG (namespace, DW_TAG_namespace) \
  DWARF_ONE_KNOWN_DW_TAG (packed_type, DW_TAG_packed_type) \
  DWARF_ONE_KNOWN_DW_TAG (partial_unit, DW_TAG_partial_unit) \
  DWARF_ONE_KNOWN_DW_TAG (pointer_type, DW_TAG_pointer_type) \
  DWARF_ONE_KNOWN_DW_TAG (ptr_to_member_type, DW_TAG_ptr_to_member_type) \
  DWARF_ONE_KNOWN_DW_TAG (reference_type, DW_TAG_reference_type) \
  DWARF_ONE_KNOWN_DW_TAG (restrict_type, DW_TAG_restrict_type) \
  DWARF_ONE_KNOWN_DW_TAG (rvalue_reference_type, DW_TAG_rvalue_reference_type) \
  DWARF_ONE_KNOWN_DW_TAG (set_type, DW_TAG_set_type) \
  DWARF_ONE_KNOWN_DW_TAG (shared_type, DW_TAG_shared_type) \
  DWARF_ONE_KNOWN_DW_TAG (skeleton_unit, DW_TAG_skeleton_unit) \
  DWARF_ONE_KNOWN_DW_TAG (string_type, DW_TAG_string_type) \
  DWARF_ONE_KNOWN_DW_TAG (structure_type, DW_TAG_structure_type) \
  DWARF_ONE_KNOWN_DW_TAG (subprogram, DW_TAG_subprogram) \
  DWARF_ONE_KNOWN_DW_TAG (subrange_type, DW_TAG_subrange_type) \
  DWARF_ONE_KNOWN_DW_TAG (subroutine_type, DW_TAG_subroutine_type) \
  DWARF_ONE_KNOWN_DW_TAG (template_alias, DW_TAG_template_alias) \
  DWARF_ONE_KNOWN_DW_TAG (template_type_parameter, DW_TAG_template_type_parameter) \
  DWARF_ONE_KNOWN_DW_TAG (template_value_parameter, DW_TAG_template_value_parameter) \
  DWARF_ONE_KNOWN_DW_TAG (thrown_type, DW_TAG_thrown_type) \
  DWARF_ONE_KNOWN_DW_TAG (try_block, DW_TAG_try_block) \
  DWARF_ONE_KNOWN_DW_TAG (type_unit, DW_TAG_type_unit) \
  DWARF_ONE_KNOWN_DW_TAG (typedef, DW_TAG_typedef) \
  DWARF_ONE_KNOWN_DW_TAG (union_type, DW_TAG_union_type) \
  DWARF_ONE_KNOWN_DW_TAG (unspecified_parameters, DW_TAG_unspecified_parameters) \
  DWARF_ONE_KNOWN_DW_TAG (unspecified_type, DW_TAG_unspecified_type) \
  DWARF_ONE_KNOWN_DW_TAG (variable, DW_TAG_variable) \
  DWARF_ONE_KNOWN_DW_TAG (variant, DW_TAG_variant) \
  DWARF_ONE_KNOWN_DW_TAG (variant_part, DW_TAG_variant_part) \
  DWARF_ONE_KNOWN_DW_TAG (volatile_type, DW_TAG_volatile_type) \
  DWARF_ONE_KNOWN_DW_TAG (with_stmt, DW_TAG_with_stmt) \
  /* End of DW_TAG_*.  */

#define DWARF_ALL_KNOWN_DW_UT \
  DWARF_ONE_KNOWN_DW_UT (compile, DW_UT_compile) \
  DWARF_ONE_KNOWN_DW_UT (partial, DW_UT_partial) \
  DWARF_ONE_KNOWN_DW_UT (skeleton, DW_UT_skeleton) \
  DWARF_ONE_KNOWN_DW_UT (split_compile, DW_UT_split_compile) \
  DWARF_ONE_KNOWN_DW_UT (split_type, DW_UT_split_type) \
  DWARF_ONE_KNOWN_DW_UT (type, DW_UT_type) \
  /* End of DW_UT_*.  */

#define DWARF_ALL_KNOWN_DW_VIRTUALITY \
  DWARF_ONE_KNOWN_DW_VIRTUALITY (none, DW_VIRTUALITY_none) \
  DWARF_ONE_KNOWN_DW_VIRTUALITY (pure_virtual, DW_VIRTUALITY_pure_virtual) \
  DWARF_ONE_KNOWN_DW_VIRTUALITY (virtual, DW_VIRTUALITY_virtual) \
  /* End of DW_VIRTUALITY_*.  */

#define DWARF_ALL_KNOWN_DW_VIS \
  DWARF_ONE_KNOWN_DW_VIS (exported, DW_VIS_exported) \
  DWARF_ONE_KNOWN_DW_VIS (local, DW_VIS_local) \
  DWARF_ONE_KNOWN_DW_VIS (qualified, DW_VIS_qualified) \
  /* End of DW_VIS_*.  */
