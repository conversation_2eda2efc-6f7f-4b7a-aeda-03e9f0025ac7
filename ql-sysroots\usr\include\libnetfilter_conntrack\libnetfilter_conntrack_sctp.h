#ifndef _LIBNETFILTER_CONNTRACK_SCTP_H_
#define _LIBNETFILTER_CONNTRACK_SCTP_H_

#ifdef __cplusplus
extern "C" {
#endif

enum sctp_state {
	SCTP_CONNTRACK_NONE,
	SCTP_<PERSON>N<PERSON><PERSON><PERSON><PERSON>_CLOSED,
	SCTP_CONNTRACK_COOKIE_WAIT,
	SCTP_CONNTRACK_COOKIE_ECHOED,
	SCTP_CONNTRACK_ESTABLISHED,
	SCTP_CONNTRACK_SHUTDOWN_SENT,
	SCTP_CONNTRACK_SHUTDOWN_RECD,
	SCTP_CONNTRACK_SHUTDOWN_ACK_SENT,
	SCTP_CONNTRACK_MAX
};

#ifdef __cplusplus
}
#endif

#endif
