GLIBC_2.0 {
  global:
	__udivdi3;
	__divdi3;
	__umoddi3;
	__moddi3;

	*;
};
GCC_3.0 {
  global:
	__mulvsi3;
	__fixdfdi;
	__umodsi3;
	__eqdf2;
	__gtsf2;
	__addvdi3;
	__fixsfdi;
	__lesf2;
	__negsf2;
	__negdi2;
	__subdf3;
	__floatdisf;
	__ltsf2;
	__divsf3;
	__gedf2;
	_Unwind_Resume;
	__mulvdi3;
	__nesf2;
	__modsi3;
	__gtdf2;
	__floatsisf;
	__extendsfdf2;
	__mulsf3;
	__muldi3;
	__ledf2;
	__negdf2;
	__floatdidf;
	_Unwind_GetLanguageSpecificData;
	__truncdfsf2;
	__udivmoddi4;
	_Unwind_GetDataRelBase;
	__ltdf2;
	__divdf3;
	_Unwind_RaiseException;
	__lshrdi3;
	__fixunsdfsi;
	_Unwind_GetRegionStart;
	__nedf2;
	__subvsi3;
	__fixunssfsi;
	__floatsidf;
	__absvsi2;
	_Unwind_DeleteException;
	__muldf3;
	__clear_cache;
	__negvsi2;
	__ashldi3;
	__addsf3;
	__ucmpdi2;
	__ashrdi3;
	__fixunsdfdi;
	__subsf3;
	__udivsi3;
	__eqsf2;
	__fixdfsi;
	__addvsi3;
	__fixunssfdi;
	__fixsfsi;
	__subvdi3;
	__absvdi2;
	__cmpdi2;
	__ffsdi2;
	_Unwind_ForcedUnwind;
	_Unwind_GetTextRelBase;
	__divsi3;
	__negvdi2;
	__gesf2;
	__adddf3;
} GLIBC_2.0;
GCC_3.3 {
  global:
	_Unwind_GetCFA;
	_Unwind_Resume_or_Rethrow;
} GCC_3.0;
GCC_3.3.1 {
  global:
	__gcc_personality_v0;
} GCC_3.3;
GCC_3.3.4 {
  global:
	__unordsf2;
	__unorddf2;
} GCC_3.3.1;
GCC_3.4 {
  global:
	__popcountsi2;
	__ctzsi2;
	__clzsi2;
	__popcountdi2;
	__ctzdi2;
	__clzdi2;
	__paritysi2;
	__paritydi2;
} GCC_3.3.4;
GCC_3.4.2 {
  global:
	__enable_execute_stack;
} GCC_3.4;
GCC_4.0.0 {
  global:
	__powidf2;
	__divsc3;
	__mulsc3;
	__divdc3;
	__powisf2;
	__muldc3;
} GCC_3.4.2;
GCC_4.2.0 {
  global:
	__floatundisf;
	__floatunsisf;
	__floatundidf;
	__floatunsidf;
} GCC_4.0.0;
GCC_4.3.0 {
  global:
	__bswapsi2;
	__emutls_register_common;
	__ffssi2;
	__bswapdi2;
	__emutls_get_address;
	_Unwind_Backtrace;
} GCC_4.2.0;
GCC_4.7.0 {
  global:
	__clrsbdi2;
	__clrsbsi2;
} GCC_4.3.0;
GCC_7.0.0 {
  global:
	__divmoddi4;
} GCC_4.7.0;
GCC_3.5 {
  global:
	__aeabi_uwrite4;
	__aeabi_uwrite8;
	__aeabi_uidiv;
	__aeabi_fcmple;
	__aeabi_llsl;
	__aeabi_fcmplt;
	__aeabi_llsr;
	__aeabi_f2d;
	_Unwind_VRS_Set;
	__aeabi_fmul;
	_Unwind_VRS_Get;
	__aeabi_idivmod;
	__aeabi_uldivmod;
	__aeabi_cdrcmple;
	__aeabi_lmul;
	__aeabi_dmul;
	__aeabi_cfcmple;
	__aeabi_f2uiz;
	__aeabi_ui2d;
	__aeabi_ui2f;
	__aeabi_drsub;
	__aeabi_dcmpge;
	__aeabi_dcmpgt;
	__aeabi_fcmpun;
	__aeabi_fcmpeq;
	__aeabi_lcmp;
	__aeabi_unwind_cpp_pr0;
	__aeabi_unwind_cpp_pr1;
	__aeabi_unwind_cpp_pr2;
	__aeabi_frsub;
	__aeabi_cfcmpeq;
	__aeabi_ldiv0;
	__aeabi_ulcmp;
	__aeabi_dcmple;
	__aeabi_f2lz;
	__aeabi_dcmplt;
	__aeabi_l2d;
	__aeabi_ldivmod;
	__aeabi_l2f;
	__aeabi_fsub;
	_Unwind_Complete;
	__aeabi_d2lz;
	__aeabi_idiv0;
	__aeabi_dsub;
	__aeabi_cdcmple;
	__aeabi_lasr;
	__aeabi_fneg;
	__aeabi_cfrcmple;
	__aeabi_uidivmod;
	__aeabi_dneg;
	__aeabi_d2ulz;
	__aeabi_f2iz;
	__aeabi_dcmpun;
	__aeabi_dcmpeq;
	__aeabi_uread4;
	__aeabi_fcmpge;
	__aeabi_uread8;
	__aeabi_fcmpgt;
	__aeabi_i2d;
	__aeabi_i2f;
	_Unwind_VRS_Pop;
	__aeabi_d2iz;
	__aeabi_fadd;
	__aeabi_fdiv;
	__aeabi_d2f;
	__aeabi_dadd;
	__aeabi_cdcmpeq;
	__aeabi_ddiv;
	__aeabi_f2ulz;
	__aeabi_idiv;
	__gnu_unwind_frame;
	__aeabi_ul2d;
	__aeabi_ul2f;
	__aeabi_d2uiz;

	*;
};
