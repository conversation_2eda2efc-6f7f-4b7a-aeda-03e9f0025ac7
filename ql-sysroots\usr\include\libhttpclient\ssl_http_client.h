#ifndef __SSL_HTTP_CLIENT_H__
#define __SSL_HTTP_CLIENT_H__

#include <mbedtls/ssl.h>
#include <mbedtls/ssl_internal.h>
#include <mbedtls/entropy.h>
#include <mbedtls/ctr_drbg.h>
#include <mbedtls/compat-1.3.h>

struct ssl_client {
	mbedtls_ssl_context ssl;
	mbedtls_ssl_config config;
	mbedtls_ctr_drbg_context ctr_drbg;
	mbedtls_entropy_context entropy;
	int fd;
};


extern struct ssl_client * ssl_client_init(int fd);

extern void ssl_client_shutdown(struct ssl_client * client);

#endif
